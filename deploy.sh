#!/bin/sh

# Check if at least one argument was provided
if [ "$#" -eq 0 ]; then
  echo "Missing commit message"
  exit 1
fi

commit_message="$1"

if [ -z "$commit_message" ]; then
  echo "Commit message is empty"
  exit 1
fi

# Optimise images
if command -v imageoptim &> /dev/null; then
  imageoptim --no-stats _site/*
else
  echo -e "\033[31mWarning: imageoptim is not installed. Skipping image optimization.\033[0m"
fi

# If all passes, push to git
git add -A
git commit -m"$commit_message"
git push