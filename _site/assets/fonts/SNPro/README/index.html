<h1>SN Pro Font Family</h1>
<p>SN Pro is a friendly sans serif typeface optimized for use with Markdown. It is based on Nunito by <PERSON>, and each character has been re-worked to improve readability and usability within a Markdown context. Originally designed by and for Supernotes.</p>
<p><a href="https://supernotes.app/open-source/sn-pro/">Visit the official website</a> for the download link, information about the design process and our sponsors.</p>
<p><img src="https://github.com/supernotes/sn-pro/assets/16340510/a6315ad0-65ea-43eb-9268-16ef6f512f8c" alt="snProChangelog"></p>
<h2>Formats</h2>
<h3>Static</h3>
<p>SN Pro is available in <code>.otf</code>, <code>.ttf</code>, <code>.woff</code> and <code>.woff2</code> formats, with nine weights ranging from Thin to Black. <a href="https://supernotes.app/open-source/sn-pro/">Please download the .zip from the official download page</a>. The included weights are:</p>
<table>
<thead>
<tr>
<th>Weight Name</th>
<th>Value</th>
</tr>
</thead>
<tbody>
<tr>
<td>Thin</td>
<td>200</td>
</tr>
<tr>
<td>Light</td>
<td>300</td>
</tr>
<tr>
<td>Book</td>
<td>350</td>
</tr>
<tr>
<td>Regular</td>
<td>400</td>
</tr>
<tr>
<td>Medium</td>
<td>500</td>
</tr>
<tr>
<td>Semi Bold</td>
<td>600</td>
</tr>
<tr>
<td>Bold</td>
<td>700</td>
</tr>
<tr>
<td>Heavy</td>
<td>800</td>
</tr>
<tr>
<td>Black</td>
<td>900</td>
</tr>
</tbody>
</table>
<h3>Variable</h3>
<p>Three variable variants are available in <code>.woff</code>, <code>.woff2</code> and <code>.ttf</code> formats:</p>
<ul>
<li>'SNPro-Variable' - all weights, including both italic and regular styles</li>
<li>'SNPro-VariableRegular' - all weights fixed in regular style</li>
<li>'SNPro-VariableItalic' - all weights fixed in italicized style</li>
</ul>
<p>If you'd like to include them in css please follow the following guidelines. This allows support for all weights and italics in almost all major browsers:</p>
<pre><code class="language-css">@font-face {
  font-family: &quot;SN Pro&quot;;
  src: url(${SNProWOFF2Regular}) format(&quot;woff2-variations&quot;);
  font-weight: 200 900;
  font-style: normal;
}
@font-face {
  font-family: &quot;SN Pro&quot;;
  src: url(${SNProWOFF2Italic}) format(&quot;woff2-variations&quot;);
  font-weight: 200 900;
  font-style: italic;
}
</code></pre>
<p><strong>Note:</strong> Using the combined <code>SNPro-Variable</code> variable font is no longer recommended on the web due to <a href="https://arrowtype.github.io/vf-slnt-test/">incompatibilities with italics across browsers</a> (mainly WebKit / Safari as of 17th September 2024).</p>
<h2>Ligatures</h2>
<p>Currently the following ligatures are supported:</p>
<pre><code class="language-js">-&gt;    →
--&gt;   ⟶
&lt;-    ←
&lt;--   ⟵
&lt;=    ≥
&gt;=    ≤
=&gt;    ⇒
==&gt;   ⟹
&lt;==   ⟸
&lt;-&gt;   ↔
&lt;--&gt;  ⟷
&lt;=&gt;   ⇔
&lt;==&gt;  ⟺
!=    ≠
</code></pre>
<p>Please make sure ligatures are active within your editor / css. Circled numbers <a href="https://github.com/supernotes/sn-pro/issues/5#issuecomment-1967209090">are not currently present in the ligature spec</a>.</p>
<h2>Stylistic Sets</h2>
<h3>Markdown Marks (ss01)</h3>
<p>While the typeface as a whole has been designed with Markdown in mind, enabling this stylistic set specifically modifies asterisks and graves to be larger and bolder while editing Markdown.</p>
<h2>Credits &amp; Links</h2>
<p>SN Pro is designed by <a href="https://tobias.so/">Tobias Whetton</a> at <a href="https://supernotes.app">Supernotes</a>. Based on <a href="https://github.com/googlefonts/nunito">Nunito</a> by Vernon Adams.</p>
