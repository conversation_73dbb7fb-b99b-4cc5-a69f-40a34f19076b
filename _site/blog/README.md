# BLOG IDEAS

## How did <PERSON><PERSON><PERSON> started
tag: blog
(also link /about to this blog post)
Wowidget history: idae, creation, usage, how it was closed down by apple - it could happen to you
enter Appio. idea still in my head. still needed. Scriptable out there used by companies (june.so) despite its limitations (mostly onboarding and single platform)

--

## How i created marketing website
tag: blog, marketing, how-to
- init prompt creation
- share chatgpt
- result
- thinking 

--

## Ios deep linking solved
tag: tech, ios, tech
- via smart app banner (app-argument) + explain how
- research of ios via (tool) + screenshots (video?)
- link to reddit / bluesky where i asked for advice
- share links to my blogpost in reddit and everywhere where ppl discuss this topic
- share on HN

### Dismissing banner
- will never come back
- no way to detect in JS - viewport check doesnt work
- reappears once the Safari data are cleared (no time sensitive, you can clear last 1hr and this will clear all dismissed banners on ios18.2)

Viewport check
https://app.appio.so/other/smart-banner-detection.html

### Inside apps

if <PERSON><PERSON> uses SFSafariViewController (Reddit), banner will be rendered.
If app uses WKWebView (chrome, firefox), banner will not show.
detection: `(window.webkit && window.webkit.messageHandlers)`

### Smart App Banner
Limitations
- no banner in private browser
- once dismissed, will not show up again

Questions?
- dismiss stored in cookie? - no, in StoreBannerTracker.plist
- dismiss per domain or subdomain? - NO, per app ID
- ios 16 introduced? - 09/22

--

## Blog: recording ios tutorial
tag: marketing, ios, how-to
- simulator
- quick time
- mose cap (+ icon config)

-- 

## Blog: ios debug console
tag: tech, ios, how-to
- how to connect via console app
- show private variables info.plist setting

using warning as info doesn't appear in console.app 

--

## Blog: Fuck you money doesn't have to mean b/millions
tag: blog
content post about my decision to go indie

recently realized I have a fuck you money what does it mean? It means something different for everyone else for me. It means giving up with my high paying job and starting unknown while supporting small family

who I am saying fuck you too? First, I should say by saying who am I not saying fuck you too this is not about my previous employer, boss or colleagues. I hold them in high respect and admiration for what are they doing daily.

spending time with my son 4months before diving into work

--

## Blog: My user's users are my users, or how the  naming works in B2B2C 
tag: blog

### Conflict:
API user: think of user as their user
API owner: think the same in their codebase

### Options:
customer, client, account, person, participant, subscriber

consumer, npc (non payable customer/character), contact, entity, identity, member, actor, player, agent,

Chatgpt: https://chatgpt.com/share/67987bf2-fc20-8006-bf0e-f7ee37f6b20c


### Researcher
Paddle: customers/sellers | users/buyers
Mews: user | guest (easy)
Stripe: user (not in API) | customer

Standard seems to be:
customer | user
https://fusionauth.io/docs/get-started/use-cases/b2b2c
https://jeffgothelf.com/blog/you-cant-control-your-customers-users/


### Final
customer (no need to control that much via API)
owned by organization (to group multiple customers)
user (to keep constant for my customers. internally customer_user)