<h1>BLOG IDEAS</h1>
<h2>How did <PERSON><PERSON><PERSON> started</h2>
<p>tag: blog
(also link /about to this blog post)
Wowidget history: idae, creation, usage, how it was closed down by apple - it could happen to you
enter Appio. idea still in my head. still needed. Scriptable out there used by companies (june.so) despite its limitations (mostly onboarding and single platform)</p>
<p>--</p>
<h2>How i created marketing website</h2>
<p>tag: blog, marketing, how-to</p>
<ul>
<li>init prompt creation</li>
<li>share chatgpt</li>
<li>result</li>
<li>thinking</li>
</ul>
<p>--</p>
<h2>Ios deep linking solved</h2>
<p>tag: tech, ios, tech</p>
<ul>
<li>via smart app banner (app-argument) + explain how</li>
<li>research of ios via (tool) + screenshots (video?)</li>
<li>link to reddit / bluesky where i asked for advice</li>
<li>share links to my blogpost in reddit and everywhere where ppl discuss this topic</li>
<li>share on HN</li>
</ul>
<h3>Dismissing banner</h3>
<ul>
<li>will never come back</li>
<li>no way to detect in JS - viewport check doesnt work</li>
<li>reappears once the Safari data are cleared (no time sensitive, you can clear last 1hr and this will clear all dismissed banners on ios18.2)</li>
</ul>
<p>Viewport check
https://app.appio.so/other/smart-banner-detection.html</p>
<h3>Inside apps</h3>
<p>if App uses SFSafariViewController (Reddit), banner will be rendered.
If app uses WKWebView (chrome, firefox), banner will not show.
detection: <code>(window.webkit &amp;&amp; window.webkit.messageHandlers)</code></p>
<h3>Smart App Banner</h3>
<p>Limitations</p>
<ul>
<li>no banner in private browser</li>
<li>once dismissed, will not show up again</li>
</ul>
<p>Questions?</p>
<ul>
<li>dismiss stored in cookie? - no, in StoreBannerTracker.plist</li>
<li>dismiss per domain or subdomain? - NO, per app ID</li>
<li>ios 16 introduced? - 09/22</li>
</ul>
<p>--</p>
<h2>Blog: recording ios tutorial</h2>
<p>tag: marketing, ios, how-to</p>
<ul>
<li>simulator</li>
<li>quick time</li>
<li>mose cap (+ icon config)</li>
</ul>
<p>--</p>
<h2>Blog: ios debug console</h2>
<p>tag: tech, ios, how-to</p>
<ul>
<li>how to connect via console app</li>
<li>show private variables info.plist setting</li>
</ul>
<p>using warning as info doesn't appear in console.app</p>
<p>--</p>
<h2>Blog: Fuck you money doesn't have to mean b/millions</h2>
<p>tag: blog
content post about my decision to go indie</p>
<p>recently realized I have a fuck you money what does it mean? It means something different for everyone else for me. It means giving up with my high paying job and starting unknown while supporting small family</p>
<p>who I am saying fuck you too? First, I should say by saying who am I not saying fuck you too this is not about my previous employer, boss or colleagues. I hold them in high respect and admiration for what are they doing daily.</p>
<p>spending time with my son 4months before diving into work</p>
<p>--</p>
<h2>Blog: My user's users are my users, or how the  naming works in B2B2C</h2>
<p>tag: blog</p>
<h3>Conflict:</h3>
<p>API user: think of user as their user
API owner: think the same in their codebase</p>
<h3>Options:</h3>
<p>customer, client, account, person, participant, subscriber</p>
<p>consumer, npc (non payable customer/character), contact, entity, identity, member, actor, player, agent,</p>
<p>Chatgpt: https://chatgpt.com/share/67987bf2-fc20-8006-bf0e-f7ee37f6b20c</p>
<h3>Researcher</h3>
<p>Paddle: customers/sellers | users/buyers
Mews: user | guest (easy)
Stripe: user (not in API) | customer</p>
<p>Standard seems to be:
customer | user
https://fusionauth.io/docs/get-started/use-cases/b2b2c
https://jeffgothelf.com/blog/you-cant-control-your-customers-users/</p>
<h3>Final</h3>
<p>customer (no need to control that much via API)
owned by organization (to group multiple customers)
user (to keep constant for my customers. internally customer_user)</p>
