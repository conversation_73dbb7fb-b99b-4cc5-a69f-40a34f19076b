
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Kit Case Study</title>

    <meta name="author" content="Appio.so">
    <meta name="description" content="Marketing campaign updates and waitlist notifications.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">

    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Open graph -->
    <meta property="og:url" content="https://appio.so">
    <meta property="og:type" content="website">
    <meta property="og:title" content="Appio: Mobile Features for Your Web App">
    <meta property="og:description" content="A<PERSON><PERSON> adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta property="og:image" content="/assets/images/social.png">

    <!-- Twitter -->
    <meta name="twitter:url" content="https://appio.so">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@appio_so">
    <meta name="twitter:creator" content="@appio_so">
    <meta name="twitter:title" content="Appio: Mobile Features for Your Web App">
    <meta name="twitter:description" content="Appio adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta name="twitter:image" content="/assets/images/social.png">
    <meta name="twitter:image:src" content="/assets/images/social.png">

    <style>@charset "UTF-8";

:root {
    --outer-background-color: 20,20,20;
    /*--background: 255,255,255;*/
    /*--box-background: 245,245,247;*/
    --background: 245,245,247;
    --box-background: 255,255,255;
    --text-color: 20,20,20;
    --border-color: 233,236,239;
    --content-width: 1200px;
    --content-padding: 1rem;
    --section-gap: 8rem;
    --scroll-margin-top: 7rem;
    --btn-color: 1,122,255;
    --btn-hover-color: 1,113,227;
    /*--btn-color: 1,113,227;*/
    /*--btn-hover-color: 0, 102, 204;*/
    --btn-shadow: inset 0 0 2px 0 #fcfcfc, 0 1px 1px 1px #d1d1d1;
    --box-shadow: rgba(13, 19, 27, 0.25) 0 0 1px 0, rgba(13, 19, 27, 0.05) 0 2px 1px 0;
    --font: "SF Pro Display", "SF Pro Icons", "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;

    /*--frame-border-width: 10;*/
    /*--frame-border-radius: 15;*/

    font-feature-settings: "liga" 1, "calt" 1; /* fix for Chrome */
}
@supports (font-variation-settings: normal) {
    :root {
        --font: "SF Pro Display", "SF Pro Icons", "InterVariable", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
    }
}
@media (max-width: 1300px) {
    :root {
        --content-width: auto;
        --content-padding: 3rem;
    }
}
@media (max-width: 790px) {
    :root {
        --content-padding: 1.5rem;
        --section-gap: 5rem;
        --scroll-margin-top: 5.4rem;
    }
}

section {
    max-width: var(--content-width);
    margin: auto;
    padding: 0 var(--content-padding);
}

html {
    font-family: var(--font);
    font-size: 106.25%;
    quotes: "“" "”";
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;

    /*background: rgb(var(--outer-background-color));*/
    /*scrollbar-color: rgb(255,255,255/.3) rgb(var(--outer-background-color));*/
}

html,
body
{
    /* prevent bouncy scroll on MacOS and iOS */
    overscroll-behavior: none;
}

/*html::before,*/
/*html::after {*/
/*    content: "";*/
/*    position: fixed;*/
/*    z-index: 100000;*/
/*    top: 0;*/
/*    bottom: 0;*/
/*    left: 0;*/
/*    right: 0;*/
/*    border: solid calc(var(--frame-border-width) * 1px) rgb(var(--outer-background-color));*/
/*    pointer-events: none;*/
/*}*/
/*html::after {*/
/*    z-index: 100001;*/
/*    border-radius: calc((var(--frame-border-width) + var(--frame-border-radius)) * 1px);*/
/*}*/

*, *::before, *::after {
    box-sizing: border-box;
}

html,body,ul,ol,li,dl,dt,dd,h1,h2,h3,h4,h5,h6,hgroup,p,blockquote,figure,form,fieldset,input,legend,pre,abbr,button {
    margin: 0;
    padding: 0;
}

dt {
    font-weight: 700;
    margin-top: 1em;
}

pre,code,address,caption,th,figcaption {
    font-size: 1em;
    font-weight: normal;
    font-style: normal;
}

code {
    font-family: "SF Mono", SFMono-Regular, ui-monospace, Menlo, monospace;
    font-weight: inherit;
    letter-spacing: 0;
    display: block;
    padding: 1em;
}

fieldset,iframe {
    border: 0;
}

caption,th {
    text-align: left;
}

main,summary,details {
    display: block;
}

audio,canvas,video,progress {
    vertical-align: baseline;
}

body {
    font-size: 1rem;
    line-height: 1.4705882353;
    font-weight: 400;
    letter-spacing: -0.022em;
    font-family: var(--font);
    background-color: rgb(var(--background));
    color: rgb(var(--text-color));
    font-style: normal;
    min-height: 100vh; /* to make background fill the whole screen */
}

body,button,input,textarea,select {
    font-synthesis: none;
    -moz-font-feature-settings: "kern";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

textarea {
    color: rgb(var(--text-color));
}

h1,h2,h3,h4,h5,h6 {
    font-weight: 700;
    color: rgb(var(--text-color));
    font-family: var(--font);
}

h1 img,h2 img,h3 img,h4 img,h5 img,h6 img {
    display: block;
    margin: 0;
}

h1+*,h2+*,h3+*,h4+*,h5+*,h6+* {
    margin-top: 0.8em;
}

h1+h1,h1+h2,h1+h3,h1+h4,h1+h5,h1+h6,h2+h1,h2+h2,h2+h3,h2+h4,h2+h5,h2+h6,h3+h1,h3+h2,h3+h3,h3+h4,h3+h5,h3+h6,h4+h1,h4+h2,h4+h3,h4+h4,h4+h5,h4+h6,h5+h1,h5+h2,h5+h3,h5+h4,h5+h5,h5+h6,h6+h1,h6+h2,h6+h3,h6+h4,h6+h5,h6+h6 {
    margin-top: 0.4em;
}

p+h1,ul+h1,ol+h1,p+h2,ul+h2,ol+h2,p+h3,ul+h3,ol+h3,p+h4,ul+h4,ol+h4,p+h5,ul+h5,ol+h5,p+h6,ul+h6,ol+h6 {
    margin-top: 1.6em;
}

p+*,ul+*,ol+*,dl+* {
    margin-top: 0.8em;
}

ul,ol {
    margin-inline-start:1.1764705882em;
}

ul ul,ul ol,ol ul,ol ol {
    margin-top: 0;
    margin-bottom: 0;
}

nav ul,nav ol {
    margin: 0;
    list-style: none;
}

li li {
    font-size: 1em;
}

b,strong {
    font-weight: 700;
}

em,i,cite,dfn {
    font-style: italic;
}

abbr {
    border: 0;
}

a {
    color: rgb(var(--text-color));
    letter-spacing: inherit;
}

/* a:link,a:visited,a:active,a:disabled {} */

a:active {
    text-decoration: none;
}

a.disabled,a :disabled {
    opacity: 0.42;
}

p+a {
    display: inline-block
}

sup,sub {
    position: relative;
    font-size: 0.6em;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

hr {
    border: none;
    height: 1px;
    background-color: rgb(var(--border-color));
    margin: 3em 0;
}

.nowrap {
    display: inline-block;
    text-decoration: inherit;
    white-space: nowrap;
}

.clear {
    clear: both;
}

h1 {
    font-size: 2.8235294118em;
    line-height: 1.1;
}

h2 {
    font-size: 1.8823529412em;
    line-height: 1.125;
    letter-spacing: .004em;
}

h3 {
    font-size: 1.6470588235em;
    line-height: 1.1428571429;
    letter-spacing: .007em;
}

h4 {
    font-size: 1.4117647059em;
    line-height: 1.1666666667;
    letter-spacing: .009em;
}

h5 {
    font-size: 1.2941176471em;
    line-height: 1.1818181818;
    letter-spacing: .01em;
}

h6 {
    font-size: 1em;
    line-height: 1.4705882353;
    letter-spacing: -.022em;
}

p {
    /* font-size: 1.1176470588em; */
    /* line-height: 1.4211026316; */
    font-weight: 400;
    letter-spacing: .012em;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
    min-width: 100%;
    font-size: .8235294118em;
    line-height: 1.4211026316;
    font-weight: 400;
    letter-spacing: -.016em;
    border-style: hidden;
    empty-cells: hide;
}

td, th {
    width: 1%;
    -webkit-hyphens: auto;
    hyphens: auto;
    min-width: 10em;
    border-style: solid;
    border-width: 1px 0;
    padding: .5882352941em;
}

th {
    border-color: rgb(var(--border-color));
    font-weight: 600;
    word-break: keep-all;
}

td {
    border-color: rgb(var(--border-color));
    word-break: break-word;
    color: var(--text-color);
}

/* Global animations */
/* clean-css ignore:start */
@keyframes fadeIn {
    from {
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
@keyframes arrowFly {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    50% {
        transform: translateX(0.5em);
        opacity: 0;
    }
    51% {
        transform: translateX(-0.5em);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}
/* clean-css ignore:end */</style>
    <style>/* Variable fonts usage:
:root { font-family: "Inter", sans-serif; }
@supports (font-variation-settings: normal) {
  :root { font-family: "InterVariable", sans-serif; font-optical-sizing: auto; }
} */
@font-face {
  font-family: InterVariable;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("/assets/fonts/inter-4-1/InterVariable.woff2") format("woff2");
}
@font-face {
  font-family: InterVariable;
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url("/assets/fonts/inter-4-1/InterVariable-Italic.woff2") format("woff2");
}

/* static fonts */
@font-face { font-family: "Inter"; font-style: normal; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Thin.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ThinItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraLight.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraLightItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Light.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-LightItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Regular.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Italic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Medium.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-MediumItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-SemiBold.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-SemiBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Bold.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-BoldItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraBold.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Black.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-BlackItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Thin.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ThinItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraLight.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraLightItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Light.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-LightItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Regular.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Italic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Medium.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-MediumItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-SemiBold.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-SemiBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Bold.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-BoldItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraBold.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Black.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-BlackItalic.woff2") format("woff2"); }

@font-feature-values InterVariable {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
@font-feature-values Inter {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
@font-feature-values InterDisplay {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
</style>

    <!-- Fonts -->
    <link rel="preload" href="/assets/fonts/inter-4-1/InterVariable.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <script>
        // Smooth scrolling only for on-page links. Instant scroll for external and back links
        window.addEventListener("load", () => {
            document.documentElement.style.scrollBehavior = "smooth";
        });

        // Adding class to elements in viewport
        window.viewPortObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add("js-in-viewport");
                    window.viewPortObserver.unobserve(entry.target);
                }
            });
        });
    </script>
</head>
<body id="top">

<style>
:root {
    --background: 255,255,255;
}
#id_dad3e036 {
    position: relative;
    padding-top: 7em;
    background-color: rgb(var(--background));
    margin-bottom: 14em; /* footer height + rounded corners bar. allow for 2 lines on mobile */
    padding-bottom: 3em;
    min-height: 100vh;
}
#id_dad3e036::before {
     content: "";
     background: rgb(var(--background));
     display: block;
     position: absolute;
     height: 3em;
     bottom: -1.5em;
     border-radius: calc(var(--frame-border-radius, 20) * 1px);;
     left: calc(var(--frame-border-width, 0) * 1px);
     right: calc(var(--frame-border-width, 0) * 1px);
}
#id_dad3e036__content {
    max-width: var(--content-width);
    padding: 0 var(--content-padding);
    margin: 0 auto;
}
@media (max-width: 790px) {
    #id_dad3e036 {
        padding-top: 5em;
    }
}
</style>

<style>
#id_5fdde3df {
    position: fixed;
    top: -1px;
    left: 0;
    right: 0;
    padding: 1em 0;
    z-index: 1000;
}
#id_5fdde3df::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(var(--background), .8);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
    backdrop-filter: saturate(180%) blur(20px);
    z-index: 1001;
}
.id_5fdde3df__scrolled {
    border-bottom: 1px solid rgba(233, 236, 239, .5);
}
#id_5fdde3df nav {
    position: sticky;
    z-index: 1002;
    width: var(--content-width);
    margin: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
@media (max-width: 1300px) {
    #id_5fdde3df nav {
        padding: 0 1em;
    }
}
#id_5fdde3df__wrapper {
    display: flex;
    align-items: center;
    gap: 1em;
}
#id_5fdde3df__logo {
    display: flex;
    align-items: center;
    gap: .35em;
    text-decoration: none;
    color: inherit;
    padding: .4em .5em .5em;
    font-weight: 700;
    font-size: 1.5em;
    user-select: none;
}
#id_5fdde3df__logo * {
    transition: fill 0.3s ease, transform 0.3s ease;
}
#id_5fdde3df__logo:hover svg {
    transform: scale(1.3);
}
#id_5fdde3df__logo:hover svg path {
    fill: none;
}
#id_5fdde3df__logo:hover svg > rect {
    stroke: #000;
    stroke-width: 60;
    rx: 160;
}
#id_5fdde3df__logo:hover svg g rect {
    fill: #000;
}
#id_5fdde3df__logo:hover svg circle {
    fill: rgb(233, 21, 45);
    stroke: rgb(var(--background));
}
#id_5fdde3df menu {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
}
#id_5fdde3df menu a {
    position: relative;
    display: block;
    text-decoration: none;
    color: inherit;
    padding: 1em;
    font-size: .9em;
    font-weight: 700;
}
#id_5fdde3df menu a::after {
    content: "";
    position: absolute;
    bottom: .5em;
    left: .5em;
    right: .5em;
    height: 1px;
    background-color: currentColor;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}
#id_5fdde3df menu a:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}
#id_5fdde3df menu a:not(:hover)::after {
    transform-origin: right;
}
#id_5fdde3df__cta {
    margin-left: 1em;
    background: rgb(var(--box-background));
    border-radius: 1.5em;
    box-shadow: var(--btn-shadow);
    user-select: none;
}
#id_5fdde3df__cta::after {
    display: none;
}
#id_5fdde3df__cta:hover {
    background: rgb(248, 249, 250);
}
#id_5fdde3df__cta:focus {
    box-shadow: none;
}
#id_5fdde3df__mobile {
    display: none;
}
@media (max-width: 790px) {
    #id_5fdde3df {
        padding: 0;
    }
    #id_5fdde3df nav {
        padding: 0;
    }
    #id_5fdde3df__logo {
        margin-right: 2.3em;
        padding: .7em .9em
    }
    #id_5fdde3df menu {
        display: none;
        padding: 0 1em;
    }
    #id_5fdde3df menu a {
        font-size: 1.5em;
        padding: .3em .7em;
    }
    #id_5fdde3df.id_5fdde3df__open {
        height: 101vh;
    }
    #id_5fdde3df.id_5fdde3df__open nav,
    #id_5fdde3df.id_5fdde3df__open #id_5fdde3df__wrapper,
    #id_5fdde3df.id_5fdde3df__open menu
    {
        display: block;
        display: block
    }
    #id_5fdde3df.id_5fdde3df__open #id_5fdde3df__cta {
        margin-left: 0;
        margin-top: .5em;
        padding: .6em 1.5em;
        text-align: center;
        display: inline-block;
    }
    #id_5fdde3df__mobile {
        display: block;
        position: absolute;
        top: .4em;
        right: .4em;
        padding: 1em;
    }
    #id_5fdde3df__mobile svg {
        display: block;
    }
}
</style>
<div id="id_5fdde3df">
    <nav>
        <div id="id_5fdde3df__wrapper">
            <a href="/" id="id_5fdde3df__logo">

                <svg width="32" height="32" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="m730.436 0c102.078.00002967 139.094 10.6288 176.413 30.5869 37.318 19.9582 66.606 49.2459 86.564 86.5641 19.957 37.319 30.587 74.335 30.587 176.413v436.872c0 102.078-10.63 139.094-30.587 176.413-19.958 37.318-49.246 66.606-86.564 86.564-37.319 19.957-74.335 30.587-176.413 30.587h-436.872c-102.078 0-139.094-10.63-176.413-30.587-37.3182-19.958-66.6059-49.246-86.5641-86.564-19.6463-36.736-30.252435-73.178-30.5790875-171.676l-.0078125-4.737v-436.872c.00002755-102.078 10.6288-139.094 30.5869-176.413 19.9582-37.3182 49.2459-66.6059 86.5641-86.5641 37.319-19.9581 74.335-30.58687246 176.413-30.5869z" fill="#000"/><rect height="650" rx="120" stroke="#fff" stroke-width="40" width="650" x="174" y="200"/><circle cx="784" cy="240" fill="#ff3b30" r="180" stroke="#000" stroke-width="80"/><g fill="#fff"><rect height="155" rx="40" width="200" x="294" y="320"/><rect height="155" rx="40" width="410" x="294" y="575"/></g></svg>

                Appio
            </a>
            <menu>
                <li>
                    <a href="/#how-it-works">How it works</a>
                </li>
                <li>
                    <a href="/#case-studies">Case studies</a>
                </li>
                <li>
                    <a href="/#pricing">Pricing</a>
                </li>
            </menu>
        </div>
        <menu>
            <li>
                <a href="https://demo.appio.so/">Demo</a>
            </li>
            <li>
                <a id="id_5fdde3df__cta" href="/list/">Get started</a>
            </li>
        </menu>
        <div id="id_5fdde3df__mobile">
            <svg width="24" height="24" viewBox="0 0 18 18">
                <polyline fill="none" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" points="2 12, 16 12">
                    <animate id="id_5fdde3df__bottom-open" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 2 12, 16 12; 2 9, 16 9; 3.5 15, 15 3.5"></animate>
                    <animate id="id_5fdde3df__bottom-close" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 3.5 15, 15 3.5; 2 9, 16 9; 2 12, 16 12"></animate>
                </polyline>
                <polyline fill="none" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" points="2 5, 16 5">
                    <animate id="id_5fdde3df__top-open" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 2 5, 16 5; 2 9, 16 9; 3.5 3.5, 15 15"></animate>
                    <animate id="id_5fdde3df__top-close" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 3.5 3.5, 15 15; 2 9, 16 9; 2 5, 16 5"></animate>
                </polyline>
            </svg>
        </div>
    </nav>
</div>
<script>
    (function () {
        // Scrolling border
        const
            header = document.getElementById("id_5fdde3df"),
            scrollClass = "id_5fdde3df__scrolled";
        window.addEventListener("scroll", () => {
            if (window.scrollY > 0) {
                header.classList.add(scrollClass);
            } else {
                header.classList.remove(scrollClass);
            }
        });

        // Mobile menu
        let open = false;
        const
            openClass = "id_5fdde3df__open",
            mobileMenu = document.getElementById("id_5fdde3df__mobile"),
            bottomOpen = document.getElementById("id_5fdde3df__bottom-open"),
            bottomClose = document.getElementById("id_5fdde3df__bottom-close"),
            topOpen = document.getElementById("id_5fdde3df__top-open"),
            topClose = document.getElementById("id_5fdde3df__top-close");

        function toggle(state) {
            open = state;
            if (open) {
                header.classList.add(openClass);
                bottomOpen.beginElement();
                topOpen.beginElement();
                document.body.style.overflow = "hidden";
            } else {
                header.classList.remove(openClass);
                bottomClose.beginElement();
                topClose.beginElement();
                document.body.style.overflow = "visible";
            }
        }

        mobileMenu.addEventListener("click", () => {
            toggle(!open)
        });

        header.querySelectorAll("a").forEach(function (a) {
            if (a.getAttribute("href").startsWith("/#")) {
                a.addEventListener("click", function () {
                    toggle(false);
                })
            }
        })
    })();
</script>

<div id="id_dad3e036">
    <div id="id_dad3e036__content">
        <style>
#id_56be2c2d header {
    margin-top: 3em;
}

#id_56be2c2d main h1 {
    display: none;
}

#id_56be2c2d header img {
    width: 50%;
}

#id_56be2c2d main {
    margin: 2em 0;
}

#id_56be2c2d .note {
    padding-bottom: 1em;
    border-bottom: solid 1px rgb(var(--border-color));
}
#id_56be2c2d .note span {
    font-weight: 600;
    text-transform: capitalize;
}
#id_56be2c2d .note span.id_56be2c2d__real {
    color: rgb(0, 61, 90);
    background: rgb(201, 240, 255);
}
#id_56be2c2d .note span.id_56be2c2d__illustrative {
    color: rgb(86, 1, 89);
    background: rgb(249, 226, 251);
}

@media (max-width: 790px) {
    #id_56be2c2d header {
        margin-top: 2em;
    }
}
</style>
<div id="id_56be2c2d">
    
        <div class="note">
            This <span class="id_56be2c2d__illustrative">illustrative</span> example,
            inspired by <span class="id_56be2c2d__real">real product needs</span>,
            shows how Appio can add value today.
        </div>
    

    <header>
        
            <img src="/assets/logos/kit.svg" alt="Kit Case Study">
        
    </header>

    <main>
        <h1>Kit</h1>
<p>Marketing campaign updates and waitlist notifications.</p>
<h2>The Challenge</h2>
<p>Kit's users needed to stay on top of their email marketing campaigns and subscriber growth. They wanted real-time updates about campaign performance and new subscriber milestones.</p>
<h2>The Solution</h2>
<p>Using Appio, Kit implemented:</p>
<ul>
<li><strong>Campaign performance notifications</strong> with open rates and clicks</li>
<li><strong>Subscriber milestone alerts</strong> for growth targets</li>
<li><strong>Waitlist notifications</strong> for product launches</li>
</ul>
<h2>The Results</h2>
<ul>
<li>Increased campaign optimization by 35%</li>
<li>Better timing for follow-up campaigns</li>
<li>Improved subscriber engagement through timely responses</li>
</ul>

    </main>
</div>


        <style>
#id_d4be03bc {
    display: block;
    margin-top: 3em;
    padding: 2em;
    background: #000;
    color: #fff;
    border-radius: 1em;
    text-decoration: none;
}
#id_d4be03bc h6 {
    color: inherit;
    font-size: 1.5em;
    font-weight: normal;
}
#id_d4be03bc p {
    margin-top: .3em;
    font-size: .9em;
    opacity: .8;
}
#id_d4be03bc p::after {
    content: "→";
    display: inline-block;
    margin-left: .25em;
    transition: transform 0.3s ease, opacity 0.3s ease;
    animation: none;
    font-weight: 500;
}
#id_d4be03bc:hover p {
    text-decoration: underline;
}
#id_d4be03bc:hover p::after {
    animation: arrowFly 0.6s ease forwards;
}
</style>
<a id="id_d4be03bc" href="/list/">
    <h6>Interested in Appio?</h6>
    <p>Get Started right now for free</p>
</a>
    </div>
</div>

<style>
#id_b3a72711__container {
    position: fixed;
    top: 0; bottom: 0;
    left: 0; right: 0;
    z-index: -1;
    color: #b0aea5;
    font-size: .8em;
    background: rgb(var(--outer-background-color));
    padding: 5em 0;
    letter-spacing: 0;
}
#id_b3a72711 footer {
    position: fixed;
    bottom: 2em;
    left: 0; right: 0;
}
#id_b3a72711 footer > div {
    max-width: var(--content-width);
    margin: 0 auto;
    padding: 0 var(--content-padding);
}
#id_b3a72711 menu {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
#id_b3a72711 menu:last-of-type {
    padding-bottom: 0.5em;
    border-bottom: solid 1px rgba(176, 174, 165, .2);
}
#id_b3a72711 a,
#id_b3a72711 span
{
    position: relative;
    display: block;
    padding: .5em 1em;
    text-decoration: none;
    color: #b0aea5;
    white-space: nowrap;
}
#id_b3a72711 a:hover {
    text-decoration: underline;
}
#id_b3a72711 span {
    opacity: .6;
}
#id_b3a72711 span::after {
    content: "→";
    font-size: .8em;
    margin-left: .3em;
}
#id_b3a72711 aside {
    padding-left: 1em;
    font-size: .85em;
    max-width: var(--content-width);
    margin: 1em auto;
}
#id_b3a72711 a[target]::after {
    position: absolute;
    content: "";
    display: inline-block;
    top: 0.5em;
    right: 0;
    border: 0.25em solid transparent;
    border-bottom-color: currentColor;
    transform: rotate(45deg);
    opacity: .5;
}
#id_b3a72711 a[target]:hover::after {
    opacity: 1;
}
</style>
<div id="id_b3a72711">
    <div id="id_b3a72711__container">
        <footer>
            <div>
                <menu>
                    <li>
                        <a href="/blog/">Blog</a>
                    </li>
                    <li>
                        <a href="/case-studies/">Case studies</a>
                    </li>
            
            
            
                    <li>
                        <a href="/contact/">Contact</a>
                    </li>
                    <li>
                        <a href="/legal">Legal</a>
                    </li>
                    <li>
                        <a href="https://docs.appio.so/" target="_blank">Docs</a>
                    </li>
                    <li>
                        <a href="https://status.appio.so/" target="_blank" rel="noopener noreferrer">Status</a>
                    </li>
                </menu>
                <menu>
                    <li>
                        <span>Integrations</span>
                    </li>
                    <li>
                        <a href="/integrations/zapier/">Zapier</a>
                    </li>
                </menu>
                <aside>
                    © 2025 Appio Limited. All rights reserved.
                </aside>
            </div>
        </footer>
    </div>
</div>



</body>
</html>
