<h1>June.so</h1>
<p>Daily usage KPIs in a live widget.</p>
<h2>The Challenge</h2>
<p>June.so users wanted to monitor their product analytics without constantly opening the dashboard. They needed quick access to key metrics throughout their day.</p>
<h2>The Solution</h2>
<p>Using Appio, June.so implemented:</p>
<ul>
<li><strong>Live KPI widgets</strong> showing daily active users, retention, and growth</li>
<li><strong>Anomaly detection notifications</strong> for unusual metric changes</li>
<li><strong>Weekly summary widgets</strong> with trend analysis</li>
</ul>
<h2>The Results</h2>
<ul>
<li>Increased data-driven decision making by 50%</li>
<li>Faster response to product issues</li>
<li>Improved team alignment on key metrics</li>
</ul>
