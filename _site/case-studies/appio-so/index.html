
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Cal.com Case Study</title>

    <meta name="author" content="Appio.so">
    <meta name="description" content="Meeting reminders via home screen widget, push notifications for new sign-ups.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">

    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Open graph -->
    <meta property="og:url" content="https://appio.so">
    <meta property="og:type" content="website">
    <meta property="og:title" content="Appio: Mobile Features for Your Web App">
    <meta property="og:description" content="A<PERSON><PERSON> adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta property="og:image" content="/assets/images/social.png">

    <!-- Twitter -->
    <meta name="twitter:url" content="https://appio.so">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@appio_so">
    <meta name="twitter:creator" content="@appio_so">
    <meta name="twitter:title" content="Appio: Mobile Features for Your Web App">
    <meta name="twitter:description" content="Appio adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta name="twitter:image" content="/assets/images/social.png">
    <meta name="twitter:image:src" content="/assets/images/social.png">

    <style>@charset "UTF-8";

:root {
    --outer-background-color: 20,20,20;
    /*--background: 252,252,252;*/
    --background: 245,245,245;
    --text-color: 20,20,20;
    --border-color: 233,236,239;
    --content-width: 1200px;
    --content-padding: 1em;
    --section-gap: 8em;
    --scroll-margin-top: 7em;
    --btn-shadow: inset 0 0 2px 0 #fcfcfc, 0 1px 1px 1px #d1d1d1;
    --box-shadow: rgba(13, 19, 27, 0.25) 0 0 1px 0, rgba(13, 19, 27, 0.05) 0 2px 1px 0;
    --font: "SF Pro Rounded", "SN Pro", -apple-system, "system-ui";
    /*--font: "SF Pro Text","SF Pro Icons","Helvetica Neue",Helvetica,Arial,sans-serif;*/

    /*--frame-border-width: 10;*/
    /*--frame-border-radius: 15;*/
}
@media (max-width: 1300px) {
    :root {
        --content-width: auto;
        --content-padding: 3em;
    }
}
@media (max-width: 790px) {
    :root {
        --content-padding: 1.5em;
        --section-gap: 4em;
        --scroll-margin-top: 5em;
    }
}

@font-face {
    font-family: SN Pro;
    font-style: normal;
    font-weight: 900;
    font-display: swap;
    src: url(/assets/fonts/sn-pro/SNPro-Black.woff2) format("woff2")
}

@font-face {
    font-family: SN Pro;
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url(/assets/fonts/sn-pro/SNPro-Bold.woff2) format("woff2")
}

@font-face {
    font-family: SN Pro;
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url(/assets/fonts/sn-pro/SNPro-Semibold.woff2) format("woff2")
}

@font-face {
    font-family: SN Pro;
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(/assets/fonts/sn-pro/SNPro-Medium.woff2) format("woff2")
}

@font-face {
    font-family: SN Pro;
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(/assets/fonts/sn-pro/SNPro-Regular.woff2) format("woff2")
}

section {
    max-width: var(--content-width);
    margin: auto;
    padding: 0 var(--content-padding);
}

html {
    font-family: var(--font);
    font-size: 106.25%;
    quotes: "“" "”";
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;

    /*background: rgb(var(--outer-background-color));*/
    /*scrollbar-color: rgb(255,255,255/.3) rgb(var(--outer-background-color));*/
}

html,
body
{
    /* prevent bouncy scroll on MacOS and iOS */
    overscroll-behavior: none;
}

/*html::before,*/
/*html::after {*/
/*    content: "";*/
/*    position: fixed;*/
/*    z-index: 100000;*/
/*    top: 0;*/
/*    bottom: 0;*/
/*    left: 0;*/
/*    right: 0;*/
/*    border: solid calc(var(--frame-border-width) * 1px) rgb(var(--outer-background-color));*/
/*    pointer-events: none;*/
/*}*/
/*html::after {*/
/*    z-index: 100001;*/
/*    border-radius: calc((var(--frame-border-width) + var(--frame-border-radius)) * 1px);*/
/*}*/

*, *::before, *::after {
    box-sizing: border-box;
}

html,body,ul,ol,li,dl,dt,dd,h1,h2,h3,h4,h5,h6,hgroup,p,blockquote,figure,form,fieldset,input,legend,pre,abbr,button {
    margin: 0;
    padding: 0;
}

dt {
    font-weight: 700;
    margin-top: 1em;
}

pre,code,address,caption,th,figcaption {
    font-size: 1em;
    font-weight: normal;
    font-style: normal;
}

code {
    font-family: "SF Mono", SFMono-Regular, ui-monospace, Menlo, monospace;
    font-weight: inherit;
    letter-spacing: 0;
    display: block;
    padding: 1em;
}

fieldset,iframe {
    border: 0;
}

caption,th {
    text-align: left;
}

main,summary,details {
    display: block;
}

audio,canvas,video,progress {
    vertical-align: baseline;
}

body {
    font-size: 1rem;
    line-height: 1.4705882353;
    font-weight: 400;
    letter-spacing: -0.022em;
    font-family: var(--font);
    background-color: rgb(var(--background));
    color: rgb(var(--text-color));
    font-style: normal;
    min-height: 100vh; /* to make background fill the whole screen */
}

body,button,input,textarea,select {
    font-synthesis: none;
    -moz-font-feature-settings: "kern";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

textarea {
    color: rgb(var(--text-color));
}

h1,h2,h3,h4,h5,h6 {
    font-weight: 900;
    color: rgb(var(--text-color));
    font-family: var(--font);
}

h1 img,h2 img,h3 img,h4 img,h5 img,h6 img {
    display: block;
    margin: 0;
}

h1+*,h2+*,h3+*,h4+*,h5+*,h6+* {
    margin-top: 0.8em;
}

h1+h1,h1+h2,h1+h3,h1+h4,h1+h5,h1+h6,h2+h1,h2+h2,h2+h3,h2+h4,h2+h5,h2+h6,h3+h1,h3+h2,h3+h3,h3+h4,h3+h5,h3+h6,h4+h1,h4+h2,h4+h3,h4+h4,h4+h5,h4+h6,h5+h1,h5+h2,h5+h3,h5+h4,h5+h5,h5+h6,h6+h1,h6+h2,h6+h3,h6+h4,h6+h5,h6+h6 {
    margin-top: 0.4em;
}

p+h1,ul+h1,ol+h1,p+h2,ul+h2,ol+h2,p+h3,ul+h3,ol+h3,p+h4,ul+h4,ol+h4,p+h5,ul+h5,ol+h5,p+h6,ul+h6,ol+h6 {
    margin-top: 1.6em;
}

p+*,ul+*,ol+*,dl+* {
    margin-top: 0.8em;
}

ul,ol {
    margin-inline-start:1.1764705882em;
}

ul ul,ul ol,ol ul,ol ol {
    margin-top: 0;
    margin-bottom: 0;
}

nav ul,nav ol {
    margin: 0;
    list-style: none;
}

li li {
    font-size: 1em;
}

b,strong {
    font-weight: 900;
}

em,i,cite,dfn {
    font-style: italic;
}

abbr {
    border: 0;
}

a {
    color: rgb(var(--text-color));
    letter-spacing: inherit;
}

/* a:link,a:visited,a:active,a:disabled {} */

a:active {
    text-decoration: none;
}

a.disabled,a :disabled {
    opacity: 0.42;
}

p+a {
    display: inline-block
}

sup,sub {
    position: relative;
    font-size: 0.6em;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

hr {
    border: none;
    height: 1px;
    background-color: rgb(var(--border-color));
    margin: 3em 0;
}

.nowrap {
    display: inline-block;
    text-decoration: inherit;
    white-space: nowrap;
}

.clear {
    clear: both;
}

h1 {
    font-size: 2.8235294118em;
    line-height: 1.1;
}

h2 {
    font-size: 1.8823529412em;
    line-height: 1.125;
    letter-spacing: .004em;
}

h3 {
    font-size: 1.6470588235em;
    line-height: 1.1428571429;
    letter-spacing: .007em;
}

h4 {
    font-size: 1.4117647059em;
    line-height: 1.1666666667;
    letter-spacing: .009em;
}

h5 {
    font-size: 1.2941176471em;
    line-height: 1.1818181818;
    letter-spacing: .01em;
}

h6 {
    font-size: 1em;
    line-height: 1.4705882353;
    letter-spacing: -.022em;
}

p {
    /* font-size: 1.1176470588em; */
    /* line-height: 1.4211026316; */
    font-weight: 400;
    letter-spacing: .012em;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
    min-width: 100%;
    font-size: .8235294118em;
    line-height: 1.4211026316;
    font-weight: 400;
    letter-spacing: -.016em;
    border-style: hidden;
    empty-cells: hide;
}

td, th {
    width: 1%;
    -webkit-hyphens: auto;
    hyphens: auto;
    min-width: 10em;
    border-style: solid;
    border-width: 1px 0;
    padding: .5882352941em;
}

th {
    border-color: rgb(var(--border-color));
    font-weight: 600;
    word-break: keep-all;
}

td {
    border-color: rgb(var(--border-color));
    word-break: break-word;
    color: var(--text-color);rgb(var(--text-color));
}

</style>

    <!-- Fonts -->
    <link rel="preload" href="/assets/fonts/sn-pro/SNPro-Black.woff2" as="font" type="font/woff2" crossorigin="anonymous">
    <link rel="preload" href="/assets/fonts/sn-pro/SNPro-Regular.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <script>
        // Smooth scrolling only for on-page links. Instant scroll for external and back links
        window.addEventListener("load", () => {
            document.documentElement.style.scrollBehavior = "smooth";
        });
    </script>
</head>
<body id="top">


<style>
    main {
        max-width: 50rem;
        margin: 0 auto;
        padding: 2em 0;
    }

    nav a {
        text-decoration: none;
        position: relative;
        padding: 1em;
        opacity: .5;
    }
    nav a:hover {
        opacity: 1;
    }
    nav a:not(:hover)::after {
        transform-origin: right;
    }
    nav a::after {
        content: "";
        position: absolute;
        bottom: .7em;
        left: 1.7em;
        right: .7em;
        height: 1px;
        background-color: currentColor;
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease;
    }
    nav a:hover::after {
        transform: scaleX(1);
        transform-origin: left;
    }

    header {
        margin-top: 2em;
        padding: 0 var(--content-padding);
    }

    section h1 {
        display: none;
    }

    header img {
        width: 50%;
    }

    section {
        margin: 2em 0;
    }

    .note {
        box-shadow: var(--box-shadow);
        background-color: #fff;
        padding: 1em;
        margin: 1em;
        border-radius: 1em;
    }
    .note span {
        color: rgb(0, 61, 90);
        background: rgb(201, 240, 255);
        font-weight: 900;
        text-transform: capitalize;
    }
</style>

<main>
    <nav>
        <a href="/#case-studies">← Back to Home</a>
    </nav>

    

    <header>
        
            <img src="/assets/logos/appio.svg" alt="Cal.com Case Study">
        
    </header>

    <section>
        <h1>Cal.com</h1>
<p>Meeting reminders via home screen widget.</p>
<h2>The Challenge</h2>
<p>Cal.com users often missed important meetings or forgot about upcoming appointments.
They needed a way to see their schedule at a glance without opening the app.</p>
<h2>The Solution</h2>
<p>Using Appio, Cal.com implemented:</p>
<ul>
<li><strong>Home screen widgets</strong> showing next meeting details</li>
<li><strong>Meeting reminder notifications</strong> 15 minutes before calls</li>
<li><strong>Daily schedule overview</strong> widget with all appointments</li>
</ul>
<h2>The Results</h2>
<ul>
<li>Reduced no-show rates by 40%</li>
<li>Improved user satisfaction with scheduling</li>
<li>Increased daily active usage through widget engagement</li>
</ul>

    </section>
    
    <nav>
        <a href="/#case-studies">← Back to Home</a>
    </nav>
</main>


<!-- Analytics -->
<script>
    !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init me ws ys ps bs capture je Di ks register register_once register_for_session unregister unregister_for_session Ps getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty Es $s createPersonProfile Is opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing Ss debug xs getPageViewId captureTraceFeedback captureTraceMetric".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
    posthog.init('phc_LBWGGE289NOf8baQoCIYP9l38yODewgjI2rZnkAfYYi', {
        api_host:'https://eu.i.posthog.com',
        person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
        autocapture: true,
        capture_pageview: true,
        cookie_domain: '.appio.so'
    })
</script>
</body>
</html>
