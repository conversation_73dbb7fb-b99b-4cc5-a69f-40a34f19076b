{%- set id = "" | componentId() -%}
<style>
#{{ id }} {
    position: relative;
    margin: var(--section-gap) auto 0;
    color: #b0aea5;
    font-size: .8em;
    background: rgb(var(--outer-background-color));
    padding: 5em 0;
}
#{{ id }}::before {
    content: "";
    background: rgb(var(--background));
    display: block;
    position: absolute;
    height: 3em;
    top: -1.5em;
    border-radius: calc(var(--frame-border-radius, 20) * 1px);;
    left: calc(var(--frame-border-width, 0) * 1px);
    right: calc(var(--frame-border-width, 0) * 1px);
}
#{{ id }} footer {
    margin: auto;
    width: var(--content-width);
    padding: 0 var(--content-padding);
}
#{{ id }} menu {
    list-style: none;
    padding: 0;
    margin: 0 0 1em;
    display: flex;
    /*gap: 1em;*/
    border-bottom: solid 1px rgba(176, 174, 165, .2);
}
#{{ id }} a {
    display: block;
    padding: 1em;
    text-decoration: none;
    color: #b0aea5;
}
#{{ id }} a:hover {
    text-decoration: underline;
}
#{{ id }} div {
    padding-left: 1em;
}
@media (max-width: 1300px) {

}
</style>
<div id="{{ id }}">
<footer>
    <menu>
        <li>
            <a href="https://docs.appio.so/">Docs</a>
        </li>
{#        <li>#}
{#            <a href="/blog">Blog</a>#}
{#        </li>#}
{#        <li>#}
{#            <a href="/integrations">Integrations</a>#}
{#        </li>#}
        <li>
            <a href="https://status.appio.so/">Status</a>
        </li>
{#        <li>#}
{#            <a href="/contact">Contact</a>#}
{#        </li>#}
{#        <li>#}
{#            <a href="/legal">Legal</a>#}
{#        </li>#}
    </menu>
    <div>
        {{ "now" | date("YYYY") }} © Appio Limited
    </div>
</footer>
</div>