---
layout: layout.njk
---

<style>
    main {
        max-width: 50rem;
        margin: 0 auto;
        padding: 2em 0 4em;
    }

    nav a {
        text-decoration: none;
        position: relative;
        padding: 1em;
        opacity: .5;
    }
    nav a:hover {
        opacity: 1;
    }
    nav a:not(:hover)::after {
        transform-origin: right;
    }
    nav a::after {
        content: "";
        position: absolute;
        bottom: .7em;
        left: 1.7em;
        right: .7em;
        height: 1px;
        background-color: currentColor;
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease;
    }
    nav a:hover::after {
        transform: scaleX(1);
        transform-origin: left;
    }

    header {
        margin-top: 2em;
        padding: 0 var(--content-padding);
    }

    section h1 {
        display: none;
    }

    header img {
        width: 50%;
    }

    section {
        margin: 2em 0;
    }

    .note {
        box-shadow: var(--box-shadow);
        background-color: #fff;
        padding: 1em;
        margin: 2.5em var(--content-padding);
        border-radius: 1em;
    }
    .note span {
        color: rgb(0, 61, 90);
        background: rgb(201, 240, 255);
        font-weight: 600;
        text-transform: capitalize;
    }
</style>

<main>
    <nav>
        <a href="/#case-studies">← Back to Home</a>
    </nav>

    {% if not real %}
        <div class="note">
            This fictional example, based on <span>real product needs</span>, demonstrates how Appio can add value today.
        </div>
    {% endif %}

    <header>
        {% if logo %}
            <img src="/assets/logos/{{ logo }}" alt="{{ title }}">
        {% else %}
            <h1>{{ title }}</h1>
        {% endif %}
    </header>

    <section>
        {{ content | safe }}
    </section>
    
    <nav>
        <a href="/#case-studies">← Back to Home</a>
    </nav>
</main>
