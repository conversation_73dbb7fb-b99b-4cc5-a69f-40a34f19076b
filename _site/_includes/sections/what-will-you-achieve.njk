{%- set id = "" | componentId() -%}
{%- set zIndex = site.baseZIndex  -%}
<style>
#{{ id }} {
    --li-margin: 10px;
    margin: var(--section-gap) auto 0;
    display: flex;
    gap: 1em;
}
#{{ id }} aside {
    max-width: 21em; /* firefox and safari */
    font-size: .9em;
}
#{{ id }} h3 {
    font-size: 2em;
    margin-top: 1em;
    padding: 0 .768em;
}
#{{ id }} ul,
#{{ id }} li
{
    list-style: none;
    margin: 0;
    padding: 0;
}
#{{ id }} ul {
    margin-top: 1.12em;
    position: relative;
}
#{{ id }} li {
    transition-property: all;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-duration: .2s;
    border-radius: 1em;
    padding: 1.2em 1em 1em 1.5em;
    cursor: pointer;
    margin-bottom: var(--li-margin);
}
#{{ id }} li:hover {
    opacity: .8;
    background-color: #fff;
}
#{{ id }} li h4 {
    font-size: 1.1em;
    font-weight: 700;
}
#{{ id }} li h4 span {
    color: rgb(173, 181, 189);
    margin-right: .3em;
}
#{{ id }} li p {
    margin-top: .3em;
}
#{{ id }} li.{{ id }}__active {
    background: #fff;
    box-shadow: var(--box-shadow);
}
#{{ id }} li.{{ id }}__active h4 {
    color: rgb(var(--btn-color));
}
#{{ id }} main {
    flex: 1;
    border-radius: 1.5em;
    background: #fff;
    border-color: #fafafa;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    justify-content: center;
    aspect-ratio: 16 / 9;
}
#{{ id }} main p {
    font-size: 3em;
    opacity: .3;
}

@media (max-width: 990px) {
    #{{ id }} {
        flex-direction: column;
        gap: calc(1em - var(--li-margin));
    }
    #{{ id }} aside {
        max-width: none;
    }
    #{{ id }} h3 {
        margin-top: 0;
        font-size: 2.5em;
        /*padding: 0 .667em;*/
        padding: 0 .578em;
    }
    #{{ id }} ul {
        scroll-margin-top: var(--scroll-margin-top);
        margin-top: 1.67em;
    }
    #{{ id }} li {
        position: relative;
        padding-right: 2.5em;
    }
    #{{ id }} ul.{{ id }}__open {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: {{ zIndex }};
        padding: 1em;
        margin: 0;
    }
    #{{id}} ul.{{ id }}__open::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(var(--background), .8);
        -webkit-backdrop-filter: saturate(180%) blur(20px);
        backdrop-filter: saturate(180%) blur(20px);
        z-index: {{ zIndex + 1 }};
    }
    #{{ id }} ul.{{ id }}__open li {
        position: relative;
        z-index: {{ zIndex + 2 }};
    }
    #{{ id }} ul:not(.{{ id }}__open) li:not(.{{ id }}__active) {
        display: none;
    }
    #{{ id }} ul:not(.{{ id }}__open) li::after {
        content: "";
        position: absolute;
        right: .8em;
        top: 55%;
        transform: translateY(-50%);
        pointer-events: none;
        opacity: .5;
        width: 1.15em;
        height: 1.15em;
        background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' stroke='currentColor'%3E%3Cpath d='M7 9L12 4' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M17 9L12 4' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M7 15L12 20' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M17 15L12 20' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
    }
}
@media (max-width: 490px) {
    #{{ id }} h3 {
        padding: 0 .556em;
        font-size: 2em;
    }
}
</style>
<div id="{{ id }}">
    <aside>
        <h3>3 things you will achieve in 1 day</h3>
        <ul id="{{ id }}__list">
            <li class="{{ id }}__active">
                <h4>Launch your first widget</h4>
                <p>Designed with our simple WYSIWYG editor.</p>
            </li>
            <li>
                <h4>Onboard your first users</h4>
                <p>And let them experience the power of Appio.</p>
            </li>
            <li>
                <h4>Send your first push notification</h4>
                <p>Via our web app.</p>
            </li>
        </ul>
    </aside>
    <main>
        {# TODO each step will have associated screenshot #}
        <p aria-hidden="true">Screenshots coming soon...</p>
    </main>
</div>
<script>
    (function () {
        // Small screen detection
        let isSmall, isOpen = false;
        const mediaQuery = window.matchMedia("(max-width: 990px)")

        function handleViewportChange(e) {
            isSmall = e.matches
        }

        handleViewportChange(mediaQuery);
        mediaQuery.addEventListener("change", handleViewportChange);

        // Item selection
        const
            listEl = document.getElementById("{{ id }}__list"),
            lis = document.querySelectorAll("#{{ id }} li"),
            activeClass = "{{ id }}__active",
            openClass = "{{ id }}__open";

        function resetActive() {
            lis.forEach(l => {
                l.classList.remove(activeClass);
            });
        }

        function switchTo(index) {
            resetActive();
            const el = document.querySelector(`#{{ id }} li:nth-child(${index + 1})`);
            el.classList.add(activeClass);

            switchImageTo(index);

            if (isSmall) {
                // Since we are scrolling to the element that is being manipulated, we need to wait for the next frame
                setTimeout(() => {
                    listEl.scrollIntoView({behavior: "instant"});
                }, 1);
            }
        }

        function switchImageTo(index) {
            // TODO: switch image
            console.log("switch image to:", index);
        }

        function open() {
            isOpen = true;
            listEl.classList.add(openClass);
            document.body.style.overflow = "hidden";
        }

        function close() {
            isOpen = false;
            listEl.classList.remove(openClass);
            document.body.style.overflow = "visible";
        }

        lis.forEach((l, i) => {
            l.addEventListener("click", () => {
                if (isSmall && !isOpen) {
                    open();
                } else {
                    switchTo(i);
                    close();
                }
            });
        });
    })();
</script>