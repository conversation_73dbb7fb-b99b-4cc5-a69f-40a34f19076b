{%- set id = "" | componentId() -%}
<style>
#{{ id }} {
    margin: var(--section-gap) auto 0;
    text-align: center;
}
#{{ id }} h2 {
    font-size: 4em;
    line-height: 1.3em;
}
#{{ id }} h2 span {
    padding: 0 .15em;
    border-radius: .2em;
}
#{{ id }} h2 span.red {
    color: rgb(128, 0, 0);
    background: rgb(255, 210, 225);
}
#{{ id }} h2 span.green {
    color: rgb(1, 89, 1);
    background: rgb(218, 249, 212);
}
#{{ id }} h2 span.blue {
    color: rgb(0, 61, 90);
    background: rgb(201, 240, 255);
}
#{{ id }} a {
    margin-top: 1.5em;
    display: inline-block;
    text-decoration: none;
    padding: .8em 1.5em;
    border-radius: 1.5em;
    font-weight: 700;
    font-size: 1.4em;
    user-select: none;
    background: rgb(var(--btn-color));
    color: #fff;
    box-shadow: rgba(0, 0, 0, 0.1) 0 1px 1px 0, rgba(0, 0, 0, 0.08) 0 2px 3px 0, rgba(0, 0, 0, 0.12) 0 4px 8px 0, rgba(6, 54, 109, 0.4) 0 -3px 2px 0 inset, rgba(255, 255, 255, 0.14) 0 2px .4px 0 inset;
}
#{{ id }} a:hover {
    background: rgb(var(--btn-hover-color));
}
#{{ id }} a:focus {
    box-shadow: none;
}
@media (max-width: 1300px) {
    #{{ id }} h2 {
        font-size: 3em;
    }
}
@media (max-width: 490px) {
    #{{ id }} h2 {
        font-size: 1.6em;
    }
}
</style>
<div id="{{ id }}">
    <h2>
        Appio is <span class="red">not</span> another app builder.<br>
        It’s <span class="blue">mobile</span>, <span class="green">simplified</span>.
    </h2>
    <a href="https://my.appio.so/">Let’s get started</a>
</div>