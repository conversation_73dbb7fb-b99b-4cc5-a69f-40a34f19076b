{%- set id = "" | componentId() -%}
<style>
#{{ id }} {
    margin: var(--section-gap) auto 0;
}
#{{ id }} h3 {
    text-align: center;
    font-size: 3em;
}
#{{ id }}__features {
    margin-top: 2.5em;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    /*grid-template-rows: repeat(2, auto);*/
    gap: 1.5em;
}
#{{ id }}__features div {
    background: #fff;
    box-shadow: var(--box-shadow);
    padding: 1.5em 2em;
    border-radius: 1em;
}
#{{ id }}__features div:hover {
    transform: translateY(-.1em);
    box-shadow: var(--box-shadow), rgba(13, 19, 27, 0.1) 0 10px 30px 0;
    transition: box-shadow 0.2s ease, transform 0.2s ease;
}
#{{ id }}__features svg {
    display: block;
    width: 2em;
    height: 2em;
    margin-bottom: .75em;
}
#{{ id }}__features span {
    display: block;
    margin-bottom: .5em;
    font-size: 1.5em;
}
#{{ id }}__features h4 {
    font-weight: 700;
    font-size: 1.3em;
}
@media (max-width: 1300px) {
    #{{ id }}__features {
        grid-template-columns: repeat(3, 1fr);
    }
}
@media (max-width: 990px) {
    #{{ id }}__features {
        grid-template-columns: repeat(2, 1fr);
    }
}
@media (max-width: 590px) {
    #{{ id }} h3 {
        font-size: 2em;
        text-align: left;
        margin: 0 .5em;
    }
    #{{ id }}__features {
        margin-top: 1.5em;
        grid-template-columns: repeat(1, 1fr);
    }
    {##{{ id }}__features svg {#}
    {#    width: 2em;#}
    {#    height: 2em;#}
    {#}#}
}
</style>
<div id="{{ id }}">

    <h3>What is included</h3>

    <div id="{{ id }}__features">
        <div>
            {% include "../../assets/symbols/notification.svg" %}
            <h4>Push notifications</h4>
            <p>Real-time delivery across iOS and Android. No native app required.</p>
        </div>
        <div>
            {% include "../../assets/symbols/widget.svg" %}
            <h4>Home screen widgets</h4>
            <p>Let users view live and personalized data (KPIs, stats, or schedule) on their home screen.</p>
        </div>
        <div>
            {% include "../../assets/symbols/gearshape.svg" %}
            <h4>iOS and Android support</h4>
            <p>Works seamlessly across all devices and platforms.</p>
        </div>
        <div>
            {% include "../../assets/symbols/brand.svg" %}
            <h4>Fully branded experience</h4>
            <p>Maintain your product identity.</p>
        </div>
        <div>
            {% include "../../assets/symbols/stairs.svg" %}
            <h4>Frictionless user onboarding</h4>
            <p>Appio takes care of the end-to-end flow. No accounts, no passwords, no confusion.</p>
        </div>
        <div>
            {% include "../../assets/symbols/archivebox.svg" %}
            <h4>Notification history</h4>
            <p>No more disappearing messages. Users can revisit missed notifications anytime.</p>
        </div>
        <div>
            {% include "../../assets/symbols/puzzle.svg" %}
            <h4>API-first and no-code friendly</h4>
            <p>Use our API or use no-code tools.</p>
        </div>
        <div>
            {% include "../../assets/symbols/lock.svg" %}
            <h4>Private by design</h4>
            <p>Built with privacy in mind. Appio never stores personal data.</p>
        </div>
    </div>
</div>