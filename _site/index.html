
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Appio: Mobile Features for Your Web App</title>

    <meta name="author" content="Appio.so">
    <meta name="description" content="A<PERSON><PERSON> adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">

    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Open graph -->
    <meta property="og:url" content="https://appio.so">
    <meta property="og:type" content="website">
    <meta property="og:title" content="Appio: Mobile Features for Your Web App">
    <meta property="og:description" content="Appio adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta property="og:image" content="/assets/images/social.png">

    <!-- Twitter -->
    <meta name="twitter:url" content="https://appio.so">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@appio_so">
    <meta name="twitter:creator" content="@appio_so">
    <meta name="twitter:title" content="Appio: Mobile Features for Your Web App">
    <meta name="twitter:description" content="Appio adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta name="twitter:image" content="/assets/images/social.png">
    <meta name="twitter:image:src" content="/assets/images/social.png">

    <style>@charset "UTF-8";

:root {
    --outer-background-color: 20,20,20;
    /*--background: 255,255,255;*/
    /*--box-background: 245,245,247;*/
    --background: 245,245,247;
    --box-background: 255,255,255;
    --text-color: 20,20,20;
    --border-color: 233,236,239;
    --content-width: 1200px;
    --content-padding: 1rem;
    --section-gap: 8rem;
    --scroll-margin-top: 7rem;
    --btn-color: 1,122,255;
    --btn-hover-color: 1,113,227;
    /*--btn-color: 1,113,227;*/
    /*--btn-hover-color: 0, 102, 204;*/
    --btn-shadow: inset 0 0 2px 0 #fcfcfc, 0 1px 1px 1px #d1d1d1;
    --box-shadow: rgba(13, 19, 27, 0.25) 0 0 1px 0, rgba(13, 19, 27, 0.05) 0 2px 1px 0;
    --font: "SF Pro Display", "SF Pro Icons", "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;

    /*--frame-border-width: 10;*/
    /*--frame-border-radius: 15;*/

    font-feature-settings: "liga" 1, "calt" 1; /* fix for Chrome */
}
@supports (font-variation-settings: normal) {
    :root {
        --font: "SF Pro Display", "SF Pro Icons", "InterVariable", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
    }
}
@media (max-width: 1300px) {
    :root {
        --content-width: auto;
        --content-padding: 3rem;
    }
}
@media (max-width: 790px) {
    :root {
        --content-padding: 1.5rem;
        --section-gap: 5rem;
        --scroll-margin-top: 5.4rem;
    }
}

section {
    max-width: var(--content-width);
    margin: auto;
    padding: 0 var(--content-padding);
}

html {
    font-family: var(--font);
    font-size: 106.25%;
    quotes: "“" "”";
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;

    /*background: rgb(var(--outer-background-color));*/
    /*scrollbar-color: rgb(255,255,255/.3) rgb(var(--outer-background-color));*/
}

html,
body
{
    /* prevent bouncy scroll on MacOS and iOS */
    overscroll-behavior: none;
}

/*html::before,*/
/*html::after {*/
/*    content: "";*/
/*    position: fixed;*/
/*    z-index: 100000;*/
/*    top: 0;*/
/*    bottom: 0;*/
/*    left: 0;*/
/*    right: 0;*/
/*    border: solid calc(var(--frame-border-width) * 1px) rgb(var(--outer-background-color));*/
/*    pointer-events: none;*/
/*}*/
/*html::after {*/
/*    z-index: 100001;*/
/*    border-radius: calc((var(--frame-border-width) + var(--frame-border-radius)) * 1px);*/
/*}*/

*, *::before, *::after {
    box-sizing: border-box;
}

html,body,ul,ol,li,dl,dt,dd,h1,h2,h3,h4,h5,h6,hgroup,p,blockquote,figure,form,fieldset,input,legend,pre,abbr,button {
    margin: 0;
    padding: 0;
}

dt {
    font-weight: 700;
    margin-top: 1em;
}

pre,code,address,caption,th,figcaption {
    font-size: 1em;
    font-weight: normal;
    font-style: normal;
}

code {
    font-family: "SF Mono", SFMono-Regular, ui-monospace, Menlo, monospace;
    font-weight: inherit;
    letter-spacing: 0;
    display: block;
    padding: 1em;
}

fieldset,iframe {
    border: 0;
}

caption,th {
    text-align: left;
}

main,summary,details {
    display: block;
}

audio,canvas,video,progress {
    vertical-align: baseline;
}

body {
    font-size: 1rem;
    line-height: 1.4705882353;
    font-weight: 400;
    letter-spacing: -0.022em;
    font-family: var(--font);
    background-color: rgb(var(--background));
    color: rgb(var(--text-color));
    font-style: normal;
    min-height: 100vh; /* to make background fill the whole screen */
}

body,button,input,textarea,select {
    font-synthesis: none;
    -moz-font-feature-settings: "kern";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

textarea {
    color: rgb(var(--text-color));
}

h1,h2,h3,h4,h5,h6 {
    font-weight: 700;
    color: rgb(var(--text-color));
    font-family: var(--font);
}

h1 img,h2 img,h3 img,h4 img,h5 img,h6 img {
    display: block;
    margin: 0;
}

h1+*,h2+*,h3+*,h4+*,h5+*,h6+* {
    margin-top: 0.8em;
}

h1+h1,h1+h2,h1+h3,h1+h4,h1+h5,h1+h6,h2+h1,h2+h2,h2+h3,h2+h4,h2+h5,h2+h6,h3+h1,h3+h2,h3+h3,h3+h4,h3+h5,h3+h6,h4+h1,h4+h2,h4+h3,h4+h4,h4+h5,h4+h6,h5+h1,h5+h2,h5+h3,h5+h4,h5+h5,h5+h6,h6+h1,h6+h2,h6+h3,h6+h4,h6+h5,h6+h6 {
    margin-top: 0.4em;
}

p+h1,ul+h1,ol+h1,p+h2,ul+h2,ol+h2,p+h3,ul+h3,ol+h3,p+h4,ul+h4,ol+h4,p+h5,ul+h5,ol+h5,p+h6,ul+h6,ol+h6 {
    margin-top: 1.6em;
}

p+*,ul+*,ol+*,dl+* {
    margin-top: 0.8em;
}

ul,ol {
    margin-inline-start:1.1764705882em;
}

ul ul,ul ol,ol ul,ol ol {
    margin-top: 0;
    margin-bottom: 0;
}

nav ul,nav ol {
    margin: 0;
    list-style: none;
}

li li {
    font-size: 1em;
}

b,strong {
    font-weight: 700;
}

em,i,cite,dfn {
    font-style: italic;
}

abbr {
    border: 0;
}

a {
    color: rgb(var(--text-color));
    letter-spacing: inherit;
}

/* a:link,a:visited,a:active,a:disabled {} */

a:active {
    text-decoration: none;
}

a.disabled,a :disabled {
    opacity: 0.42;
}

p+a {
    display: inline-block
}

sup,sub {
    position: relative;
    font-size: 0.6em;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

hr {
    border: none;
    height: 1px;
    background-color: rgb(var(--border-color));
    margin: 3em 0;
}

.nowrap {
    display: inline-block;
    text-decoration: inherit;
    white-space: nowrap;
}

.clear {
    clear: both;
}

h1 {
    font-size: 2.8235294118em;
    line-height: 1.1;
}

h2 {
    font-size: 1.8823529412em;
    line-height: 1.125;
    letter-spacing: .004em;
}

h3 {
    font-size: 1.6470588235em;
    line-height: 1.1428571429;
    letter-spacing: .007em;
}

h4 {
    font-size: 1.4117647059em;
    line-height: 1.1666666667;
    letter-spacing: .009em;
}

h5 {
    font-size: 1.2941176471em;
    line-height: 1.1818181818;
    letter-spacing: .01em;
}

h6 {
    font-size: 1em;
    line-height: 1.4705882353;
    letter-spacing: -.022em;
}

p {
    /* font-size: 1.1176470588em; */
    /* line-height: 1.4211026316; */
    font-weight: 400;
    letter-spacing: .012em;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
    min-width: 100%;
    font-size: .8235294118em;
    line-height: 1.4211026316;
    font-weight: 400;
    letter-spacing: -.016em;
    border-style: hidden;
    empty-cells: hide;
}

td, th {
    width: 1%;
    -webkit-hyphens: auto;
    hyphens: auto;
    min-width: 10em;
    border-style: solid;
    border-width: 1px 0;
    padding: .5882352941em;
}

th {
    border-color: rgb(var(--border-color));
    font-weight: 600;
    word-break: keep-all;
}

td {
    border-color: rgb(var(--border-color));
    word-break: break-word;
    color: var(--text-color);
}

/* Global animations */
/* clean-css ignore:start */
@keyframes fadeIn {
    from {
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
@keyframes arrowFly {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    50% {
        transform: translateX(0.5em);
        opacity: 0;
    }
    51% {
        transform: translateX(-0.5em);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}
/* clean-css ignore:end */</style>
    <style>/* Variable fonts usage:
:root { font-family: "Inter", sans-serif; }
@supports (font-variation-settings: normal) {
  :root { font-family: "InterVariable", sans-serif; font-optical-sizing: auto; }
} */
@font-face {
  font-family: InterVariable;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("/assets/fonts/inter-4-1/InterVariable.woff2") format("woff2");
}
@font-face {
  font-family: InterVariable;
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url("/assets/fonts/inter-4-1/InterVariable-Italic.woff2") format("woff2");
}

/* static fonts */
@font-face { font-family: "Inter"; font-style: normal; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Thin.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ThinItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraLight.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraLightItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Light.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-LightItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Regular.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Italic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Medium.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-MediumItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-SemiBold.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-SemiBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Bold.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-BoldItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraBold.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Black.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-BlackItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Thin.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ThinItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraLight.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraLightItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Light.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-LightItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Regular.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Italic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Medium.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-MediumItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-SemiBold.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-SemiBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Bold.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-BoldItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraBold.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Black.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-BlackItalic.woff2") format("woff2"); }

@font-feature-values InterVariable {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
@font-feature-values Inter {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
@font-feature-values InterDisplay {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
</style>

    <!-- Fonts -->
    <link rel="preload" href="/assets/fonts/inter-4-1/InterVariable.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <script>
        // Smooth scrolling only for on-page links. Instant scroll for external and back links
        window.addEventListener("load", () => {
            document.documentElement.style.scrollBehavior = "smooth";
        });

        // Adding class to elements in viewport
        window.viewPortObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add("js-in-viewport");
                    window.viewPortObserver.unobserve(entry.target);
                }
            });
        });
    </script>
</head>
<body id="top">

<style>
#id_6d8b201f {
    position: relative;
    padding-top: 6em;
    background: rgb(var(--background));
    margin-bottom: 14em; /* footer height + rounded corners bar. allow for 2 lines on mobile */
    padding-bottom: 3em;
    min-height: 100vh;
}
#id_6d8b201f::before {
    content: "";
    background: rgb(var(--background));
    display: block;
    position: absolute;
    height: 3em;
    bottom: -1.5em;
    border-radius: calc(var(--frame-border-radius, 20) * 1px);;
    left: calc(var(--frame-border-width, 0) * 1px);
    right: calc(var(--frame-border-width, 0) * 1px);
}
@media (max-width: 790px) {
    #id_6d8b201f {
        padding-top: 4em;
    }
}
</style>
<style>
#id_00dfd4bd {
    position: fixed;
    top: -1px;
    left: 0;
    right: 0;
    padding: 1em 0;
    z-index: 1000;
}
#id_00dfd4bd::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(var(--background), .8);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
    backdrop-filter: saturate(180%) blur(20px);
    z-index: 1001;
}
.id_00dfd4bd__scrolled {
    border-bottom: 1px solid rgba(233, 236, 239, .5);
}
#id_00dfd4bd nav {
    position: sticky;
    z-index: 1002;
    width: var(--content-width);
    margin: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
@media (max-width: 1300px) {
    #id_00dfd4bd nav {
        padding: 0 1em;
    }
}
#id_00dfd4bd__wrapper {
    display: flex;
    align-items: center;
    gap: 1em;
}
#id_00dfd4bd__logo {
    display: flex;
    align-items: center;
    gap: .35em;
    text-decoration: none;
    color: inherit;
    padding: .4em .5em .5em;
    font-weight: 700;
    font-size: 1.5em;
    user-select: none;
}
#id_00dfd4bd__logo * {
    transition: fill 0.3s ease, transform 0.3s ease;
}
#id_00dfd4bd__logo:hover svg {
    transform: scale(1.3);
}
#id_00dfd4bd__logo:hover svg path {
    fill: none;
}
#id_00dfd4bd__logo:hover svg > rect {
    stroke: #000;
    stroke-width: 60;
    rx: 160;
}
#id_00dfd4bd__logo:hover svg g rect {
    fill: #000;
}
#id_00dfd4bd__logo:hover svg circle {
    fill: rgb(233, 21, 45);
    stroke: rgb(var(--background));
}
#id_00dfd4bd menu {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
}
#id_00dfd4bd menu a {
    position: relative;
    display: block;
    text-decoration: none;
    color: inherit;
    padding: 1em;
    font-size: .9em;
    font-weight: 700;
}
#id_00dfd4bd menu a::after {
    content: "";
    position: absolute;
    bottom: .5em;
    left: .5em;
    right: .5em;
    height: 1px;
    background-color: currentColor;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}
#id_00dfd4bd menu a:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}
#id_00dfd4bd menu a:not(:hover)::after {
    transform-origin: right;
}
#id_00dfd4bd__cta {
    margin-left: 1em;
    background: rgb(var(--box-background));
    border-radius: 1.5em;
    box-shadow: var(--btn-shadow);
    user-select: none;
}
#id_00dfd4bd__cta::after {
    display: none;
}
#id_00dfd4bd__cta:hover {
    background: rgb(248, 249, 250);
}
#id_00dfd4bd__cta:focus {
    box-shadow: none;
}
#id_00dfd4bd__mobile {
    display: none;
}
@media (max-width: 790px) {
    #id_00dfd4bd {
        padding: 0;
    }
    #id_00dfd4bd nav {
        padding: 0;
    }
    #id_00dfd4bd__logo {
        margin-right: 2.3em;
        padding: .7em .9em
    }
    #id_00dfd4bd menu {
        display: none;
        padding: 0 1em;
    }
    #id_00dfd4bd menu a {
        font-size: 1.5em;
        padding: .3em .7em;
    }
    #id_00dfd4bd.id_00dfd4bd__open {
        height: 101vh;
    }
    #id_00dfd4bd.id_00dfd4bd__open nav,
    #id_00dfd4bd.id_00dfd4bd__open #id_00dfd4bd__wrapper,
    #id_00dfd4bd.id_00dfd4bd__open menu
    {
        display: block;
        display: block
    }
    #id_00dfd4bd.id_00dfd4bd__open #id_00dfd4bd__cta {
        margin-left: 0;
        margin-top: .5em;
        padding: .6em 1.5em;
        text-align: center;
        display: inline-block;
    }
    #id_00dfd4bd__mobile {
        display: block;
        position: absolute;
        top: .4em;
        right: .4em;
        padding: 1em;
    }
    #id_00dfd4bd__mobile svg {
        display: block;
    }
}
</style>
<div id="id_00dfd4bd">
    <nav>
        <div id="id_00dfd4bd__wrapper">
            <a href="/" id="id_00dfd4bd__logo">

                <svg width="32" height="32" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="m730.436 0c102.078.00002967 139.094 10.6288 176.413 30.5869 37.318 19.9582 66.606 49.2459 86.564 86.5641 19.957 37.319 30.587 74.335 30.587 176.413v436.872c0 102.078-10.63 139.094-30.587 176.413-19.958 37.318-49.246 66.606-86.564 86.564-37.319 19.957-74.335 30.587-176.413 30.587h-436.872c-102.078 0-139.094-10.63-176.413-30.587-37.3182-19.958-66.6059-49.246-86.5641-86.564-19.6463-36.736-30.252435-73.178-30.5790875-171.676l-.0078125-4.737v-436.872c.00002755-102.078 10.6288-139.094 30.5869-176.413 19.9582-37.3182 49.2459-66.6059 86.5641-86.5641 37.319-19.9581 74.335-30.58687246 176.413-30.5869z" fill="#000"/><rect height="650" rx="120" stroke="#fff" stroke-width="40" width="650" x="174" y="200"/><circle cx="784" cy="240" fill="#ff3b30" r="180" stroke="#000" stroke-width="80"/><g fill="#fff"><rect height="155" rx="40" width="200" x="294" y="320"/><rect height="155" rx="40" width="410" x="294" y="575"/></g></svg>

                Appio
            </a>
            <menu>
                <li>
                    <a href="/#how-it-works">How it works</a>
                </li>
                <li>
                    <a href="/#case-studies">Case studies</a>
                </li>
                <li>
                    <a href="/#pricing">Pricing</a>
                </li>
            </menu>
        </div>
        <menu>
            <li>
                <a href="https://demo.appio.so/">Demo</a>
            </li>
            <li>
                <a id="id_00dfd4bd__cta" href="/list/">Get started</a>
            </li>
        </menu>
        <div id="id_00dfd4bd__mobile">
            <svg width="24" height="24" viewBox="0 0 18 18">
                <polyline fill="none" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" points="2 12, 16 12">
                    <animate id="id_00dfd4bd__bottom-open" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 2 12, 16 12; 2 9, 16 9; 3.5 15, 15 3.5"></animate>
                    <animate id="id_00dfd4bd__bottom-close" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 3.5 15, 15 3.5; 2 9, 16 9; 2 12, 16 12"></animate>
                </polyline>
                <polyline fill="none" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" points="2 5, 16 5">
                    <animate id="id_00dfd4bd__top-open" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 2 5, 16 5; 2 9, 16 9; 3.5 3.5, 15 15"></animate>
                    <animate id="id_00dfd4bd__top-close" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 3.5 3.5, 15 15; 2 9, 16 9; 2 5, 16 5"></animate>
                </polyline>
            </svg>
        </div>
    </nav>
</div>
<script>
    (function () {
        // Scrolling border
        const
            header = document.getElementById("id_00dfd4bd"),
            scrollClass = "id_00dfd4bd__scrolled";
        window.addEventListener("scroll", () => {
            if (window.scrollY > 0) {
                header.classList.add(scrollClass);
            } else {
                header.classList.remove(scrollClass);
            }
        });

        // Mobile menu
        let open = false;
        const
            openClass = "id_00dfd4bd__open",
            mobileMenu = document.getElementById("id_00dfd4bd__mobile"),
            bottomOpen = document.getElementById("id_00dfd4bd__bottom-open"),
            bottomClose = document.getElementById("id_00dfd4bd__bottom-close"),
            topOpen = document.getElementById("id_00dfd4bd__top-open"),
            topClose = document.getElementById("id_00dfd4bd__top-close");

        function toggle(state) {
            open = state;
            if (open) {
                header.classList.add(openClass);
                bottomOpen.beginElement();
                topOpen.beginElement();
                document.body.style.overflow = "hidden";
            } else {
                header.classList.remove(openClass);
                bottomClose.beginElement();
                topClose.beginElement();
                document.body.style.overflow = "visible";
            }
        }

        mobileMenu.addEventListener("click", () => {
            toggle(!open)
        });

        header.querySelectorAll("a").forEach(function (a) {
            if (a.getAttribute("href").startsWith("/#")) {
                a.addEventListener("click", function () {
                    toggle(false);
                })
            }
        })
    })();
</script>
<div id="id_6d8b201f">
    <style>
    .id_093bc9a8__fade-in {
        opacity: 0;
    }
    .id_093bc9a8__fade-in.js-in-viewport {
        animation: fadeIn .3s ease-out var(--fade-in-delay, .2s) forwards;
    }
</style>
<noscript>
    <style>
        .id_093bc9a8__fade-in {
            opacity: 1;
        }
    </style>
</noscript>

<section class="id_093bc9a8__fade-in" style="--fade-in-delay: .1s;">
    <style>
#id_a9333624 {
    padding-top: 4em;
}
#id_a9333624 h1 {
    font-size: 3.5em;

    /*opacity: 0;*/
    /*transform: translateY(30px);*/
    /*animation: fadeIn .5s ease-out .3s forwards;*/
}
#id_a9333624 h2 {
    font-size: 2em;
    line-height: 1.4;
    font-weight: 400;

    opacity: 0;
    animation: fadeIn .3s ease-out .3s forwards;
}
#id_a9333624 h2 span.id_a9333624__no-wrap {
    white-space: nowrap;
}
#id_a9333624 h2 span.id_a9333624__tag {
    display: inline-block;
    color: rgb(86, 1, 89);
    background: rgb(249, 226, 251);
    /*text-transform: capitalize;*/
    font-weight: 600;
}
#id_a9333624 h2 span.id_a9333624__tag_left {
    padding-left: .25em;
    border-top-left-radius: .2em;
    border-bottom-left-radius: .2em;
}
#id_a9333624 h2 span.id_a9333624__tag_right {
    padding-right: .25em;
    border-top-right-radius: .2em;
    border-bottom-right-radius: .2em;
}
#id_a9333624 h2 span.id_a9333624__tag_blue {
    color: rgb(0, 61, 90);
    background: rgb(201, 240, 255);
}
#id_a9333624 h2 span.id_a9333624__scratch {
    position: relative;
    white-space: nowrap;
}
#id_a9333624 h2 span.id_a9333624__scratch:after {
    content: "";
    position: absolute;
    bottom: .45em;
    left: -.1em;
    right: -.2em;
    height: .09em;
    background-color: currentColor;
    border-radius: 1em;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform .1s ease-in 1s; /* ease-out */
}
#id_a9333624 h2 span.id_a9333624__scratch.js-in-viewport:after {
    transform: scaleX(1);
}
#id_a9333624 div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 3em;

    opacity: 0;
    animation: fadeIn .2s ease-out .4s forwards;
}
#id_a9333624 nav {
    display: flex;
    gap: 1em;
}
#id_a9333624 a {
    display: block;
    text-decoration: none;
    padding: .8em 1.5em;
    border-radius: 1.5em;
    box-shadow: var(--box-shadow);
    font-weight: 700;
    font-size: 1.4em;
    color: inherit;
    background: rgb(var(--box-background));
    user-select: none;
    text-align: center;
}
#id_a9333624 a:hover {
    background: rgb(248, 249, 250);
}
#id_a9333624 a.id_a9333624__main {
    background: rgb(var(--btn-color));
    color: #fff;
    box-shadow: rgba(0, 0, 0, 0.1) 0 1px 1px 0, rgba(0, 0, 0, 0.08) 0 2px 3px 0, rgba(0, 0, 0, 0.12) 0 4px 8px 0, rgba(6, 54, 109, 0.4) 0 -3px 2px 0 inset, rgba(255, 255, 255, 0.14) 0 2px .4px 0 inset;
}
#id_a9333624 a.id_a9333624__main:hover {
    background: rgb(var(--btn-hover-color));
}
#id_a9333624 a:focus {
    box-shadow: none;
}
#id_a9333624 aside {
    display: flex;
    align-items: center;
    gap: .5em;
    font-size: 1.1em;
    font-weight: 500;
    color: rgb(110,110,110);
    white-space: nowrap;
    user-select: none;
    text-transform: lowercase;
}
#id_a9333624 aside svg {
    height: 1.5em;
    display: block;
}
@media (max-width: 1300px) {
    #id_a9333624 {
        padding-top: 2em;
    }
}
@media (max-width: 790px) {
    #id_a9333624 {
        padding-top: 2em;
    }
    #id_a9333624 h1 {
        font-size: 3em;
    }
    #id_a9333624 h2 {
        font-size: 1.4em;
        margin-top: .6em;
    }
    #id_a9333624 aside {
        display: none;
    }
}
@media (max-width: 490px) {
    #id_a9333624 h1 {
        font-size: 2.3em;
    }
    #id_a9333624 h2 {
        font-size: 1.1em;
        margin-top: 1.5em;
    }
    #id_a9333624 div {
        margin-top: 2em;
    }
    #id_a9333624 nav {
        flex-direction: column;
        width: 100%;
    }
    #id_a9333624 a {
        font-size: 1.2em;
    }
}
</style>
<div id="id_a9333624">





    <h1>
        Your customers are on mobile.<br>
        What is stopping you?

    </h1>
    <h2>
        Appio adds mobile features to your web app, without <span class="id_a9333624__scratch">building an app</span>.<br>
        Send <span class="id_a9333624__tag id_a9333624__tag_left">push&nbsp;</span><span class="id_a9333624__no-wrap"><span class="id_a9333624__tag id_a9333624__tag_right">notifications</span>,</span>
        create <span class="id_a9333624__tag id_a9333624__tag_left">home</span><span class="id_a9333624__tag">&nbsp;screen&nbsp;</span><span class="id_a9333624__no-wrap"><span class="id_a9333624__tag id_a9333624__tag_right">widgets</span>,</span>
        launch <span class="id_a9333624__tag id_a9333624__tag_left id_a9333624__tag_blue">in&nbsp;</span><span class="id_a9333624__no-wrap"><span class="id_a9333624__tag id_a9333624__tag_right id_a9333624__tag_blue">minutes</span>.</span>

    </h2>
    <div>
        <nav>
            <a class="id_a9333624__main" href="/list/">Start Now</a>
            <a href="https://demo.appio.so/">Try the Demo</a>
        </nav>
        <aside aria-hidden="true">
            Works on
            <svg viewBox="0 0 842 1000" xmlns="http://www.w3.org/2000/svg"><path d="m824.66636 779.30363c-15.12299 34.93724-33.02368 67.09674-53.7638 96.66374-28.27076 40.3074-51.4182 68.2078-69.25717 83.7012-27.65347 25.4313-57.2822 38.4556-89.00964 39.1963-22.77708 0-50.24539-6.4813-82.21973-19.629-32.07926-13.0861-61.55985-19.5673-88.51583-19.5673-28.27075 0-58.59083 6.4812-91.02193 19.5673-32.48053 13.1477-58.64639 19.9994-78.65196 20.6784-30.42501 1.29623-60.75123-12.0985-91.02193-40.2457-19.32039-16.8514-43.48632-45.7394-72.43607-86.6641-31.060778-43.7024-56.597041-94.37983-76.602609-152.15586-21.425275-62.40552-32.165691-122.83578-32.165691-181.34016 0-67.01648 14.481044-124.8172 43.486336-173.25401 22.795604-38.90621 53.121844-69.59664 91.077464-92.12684 37.95566-22.53016 78.96676-34.01129 123.1321-34.74585 24.16591 0 55.85633 7.47508 95.23784 22.166 39.27042 14.74029 64.48571 22.21538 75.54091 22.21538 8.26518 0 36.27668-8.7405 83.7629-26.16587 44.90607-16.16001 82.80614-22.85118 113.85458-20.21546 84.13326 6.78992 147.34122 39.95559 189.37699 99.70686-75.24463 45.59122-112.46573 109.4473-111.72502 191.36456.67899 63.8067 23.82643 116.90384 69.31888 159.06309 20.61664 19.56727 43.64066 34.69027 69.2571 45.4307-5.55531 16.11062-11.41933 31.54225-17.65372 46.35662zm-192.9571-759.29793c0 50.01141-18.27108 96.70693-54.6897 139.92782-43.94932 51.38118-97.10817 81.07162-154.75459 76.38659-.73454-5.99983-1.16045-12.31444-1.16045-18.95003 0-48.01091 20.9006-99.39207 58.01678-141.40314 18.53027-21.27094 42.09746-38.95744 70.67685-53.0663 28.51765-13.89835 55.49215-21.58443 80.86173-22.90064.74076 6.68575 1.04938 13.37191 1.04938 20.00505z" fill="#b3b3b3"/></svg>
            <svg viewBox="-151 -74 302 353" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g fill="#3ddc84"><use stroke="#f8f8f8" stroke-width="28.8" xlink:href="#c"/><use transform="scale(-1 1)" xlink:href="#a"/><g id="a" stroke="#f8f8f8" stroke-width="14.4"><rect height="86" rx="6.5" transform="matrix(.87461971 .48480962 -.48480962 .87461971 0 0)" width="13" x="14" y="-86"/><rect id="b" height="133" rx="24" width="48" x="-143" y="41"/><use x="85" xlink:href="#b" y="97"/></g><g id="c"><ellipse cy="41" rx="91" ry="84"/><rect height="182" rx="22" width="182" x="-91" y="20"/></g></g><g fill="#f8f8f8" stroke="#f8f8f8" stroke-width="14.4"><path d="m-95 44.5h190"/><circle cx="-42" r="4"/><circle cx="42" r="4"/></g></svg>
            <span style="opacity:.5"><svg viewBox="0 0 12.9102 20.6934" xmlns="http://www.w3.org/2000/svg"><path d="m2.65625 20.6738h7.23633c1.59182 0 2.65622-1.0156 2.65622-2.539v-15.59574c0-1.52344-1.0644-2.53906-2.65622-2.53906h-7.23633c-1.5918 0-2.65625 1.01562-2.65625 2.53906v15.59574c0 1.5234 1.06445 2.539 2.65625 2.539zm.20508-1.5722c-.83008 0-1.28906-.4395-1.28906-1.2207v-15.08793c0-.78125.45898-1.2207 1.28906-1.2207h6.83594c.82033 0 1.27933.43945 1.27933 1.2207v15.08793c0 .7812-.459 1.2207-1.27933 1.2207zm1.34765-.7129h4.1504c.26367 0 .44921-.1856.44921-.459 0-.2735-.18554-.4492-.44921-.4492h-4.1504c-.26367 0-.45898.1757-.45898.4492 0 .2734.19531.459.45898.459zm.88868-14.74612h2.36328c.38086 0 .68359-.30274.68359-.69336 0-.38086-.30273-.6836-.68359-.6836h-2.36328c-.39063 0-.69336.30274-.69336.6836 0 .39062.30273.69336.69336.69336z" fill-opacity=".85"/></svg></span>
            <span style="opacity:.5"><svg viewBox="0 0 23.3887 17.998" xmlns="http://www.w3.org/2000/svg"><path d="m7.74414 15.6738h7.54886c.2636 0 .4492-.1758.4492-.4492s-.1856-.459-.4492-.459h-7.54886c-.26367 0-.45898.1856-.45898.459s.19531.4492.45898.4492zm-4.67773 2.3242h16.89449c2.0508 0 3.0664-1.0156 3.0664-3.0273v-11.92382c0-2.01172-1.0156-3.0273488-3.0664-3.0273488h-16.89449c-2.04102 0-3.06641 1.0058588-3.06641 3.0273488v11.92382c0 2.0215 1.02539 3.0273 3.06641 3.0273zm.01953-1.5722c-.97656 0-1.51367-.5176-1.51367-1.5332v-11.7676c0-1.01562.53711-1.5332 1.51367-1.5332h16.85546c.9668 0 1.5137.51758 1.5137 1.5332v11.7676c0 1.0156-.5469 1.5332-1.5137 1.5332z" fill-opacity=".85"/></svg></span>
            
        </aside>
    </div>
</div>
<script>
(function () {
    document.querySelectorAll(".id_a9333624__scratch").forEach(el => {
        window.viewPortObserver.observe(el);
    });
})();
</script>
</section>
<section class="id_093bc9a8__fade-in">
    <style>
#how-it-works {
    scroll-margin-top: var(--scroll-margin-top);
}
#how-it-works p {
    margin-top: 0;
}
#id_505c742a {
    --li-margin: 10px;
    margin-top: var(--section-gap);
}
#id_505c742a > div {
    margin: 0 1.3em;
}
#id_505c742a h3 {
    font-size: 3em;
    line-height: 1.2em;
}
#id_505c742a main {
    margin-top: 1.5em;
    display: flex;
    gap: 1em;
}
#id_505c742a aside {
    max-width: 21em; /* firefox and safari */
    font-size: .9em;
}
#id_505c742a aside > p {
    margin-left: 1.5em;
    font-size: .93em;
}
#id_505c742a ul,
#id_505c742a li
{
    list-style: none;
    margin: 0;
    padding: 0;
}
#id_505c742a ul {
    position: relative;
}
#id_505c742a li {
    transition-property: all;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-duration: .2s;
    border-radius: 1em;
    padding: 1em 1em 1em 1.5em;
    cursor: pointer;
    margin-bottom: var(--li-margin);
}
#id_505c742a li:hover {
    opacity: .8;
    background-color: rgb(var(--box-background));
}
#id_505c742a li h4 {
    font-size: 1.1em;
    font-weight: 700;
}
#id_505c742a li h4 span {
    color: rgb(110,110,110);
    margin-right: .3em;
    user-select: none;
}
#id_505c742a li p {
    margin-top: .3em;
}
#id_505c742a li.id_505c742a__active {
    background: rgb(var(--box-background));
    box-shadow: var(--box-shadow);
}
#id_505c742a li.id_505c742a__active h4 {
    color: rgb(var(--btn-color));
}
#id_505c742a__select-wrapper {
    position: relative;
    display: inline-block;
}
#id_505c742a__select-wrapper::after {
    content: "";
    position: absolute;
    right: .7em;
    top: 55%;
    transform: translateY(-50%);
    font-size: .5em;
    pointer-events: none;
    opacity: .5;
    width: 1em;
    height: 1em;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' stroke='currentColor'%3E%3Cpath d='M7 9L12 4' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M17 9L12 4' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M7 15L12 20' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M17 15L12 20' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
}
#id_505c742a select {
    cursor: pointer;
    border: solid 1px transparent;
    background-color: rgb(218, 249, 212);
    color: rgb(1, 89, 1);
    font-size: .7em;
    font-weight: 700;
    border-radius: .5em;
    padding: .3em 1.3em .3em .6em;
    appearance: none;
    overflow: hidden;
    text-overflow: ellipsis;
    box-shadow: var(--box-shadow);
    vertical-align: middle;
}
/* class `js` set by javascript */
#id_505c742a__select-wrapper.js {
    position: relative;
}
#id_505c742a__select-wrapper.js select {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
}
#id_505c742a__select-wrapper.js span {
    display: block;
    background-color: rgb(218, 249, 212);
    color: rgb(1, 89, 1);
    border-radius: .5em;
    padding: .2em 1.2em .2em .4em;
    cursor: pointer;
    text-align: center;
}
#id_505c742a__select-wrapper.js:hover span {
    background-color: rgba(218, 249, 212, .5);
}
#id_505c742a__video {
    flex: 1;
    border-radius: 1.5em;
    background: rgb(var(--box-background));
    border-color: #fafafa;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    justify-content: center;
    aspect-ratio: 16 / 9;
    overflow: hidden;
}
#id_505c742a__video p {
    font-size: 3em;
    opacity: .3;
}

@supports (text-orientation: upright) {
    #id_505c742a__for-you::before,
    #id_505c742a__for-your-users::before
    {
        writing-mode: vertical-rl;
        text-orientation: upright;
        opacity: .1;
        font-weight: 700;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        transition: top 0.3s ease;
        white-space: nowrap;
        font-size: 1.3em;
        left: -2em;
    }
    #id_505c742a__for-you::before {
        content: "YOU";
        /* `--top-extra` is modified via JavaScript */
        top: calc(.75em + var(--top-extra, 0px));
        transform: none;
    }
    #id_505c742a__for-your-users::before {
        content: "YOUR USERS";
    }
}

@media (max-width: 1150px) {
    #id_505c742a h3 {
        font-size: 2.5em;
    }
}
/* Breakpoint value used in JavaScript bellow as well */
@media (max-width: 990px) {
    #id_505c742a main {
        scroll-margin-top: var(--scroll-margin-top);
        flex-direction: column;
        margin-top: 1em;
        gap: calc(1em - var(--li-margin));
    }
    #id_505c742a__for-you::before,
    #id_505c742a__for-your-users::before
    {
        content: "";
        display: none;
    }
    #id_505c742a__select-wrapper {
        display: block;
        margin: .2em -.5em 0;
    }
    #id_505c742a__select-wrapper span {
        border-right: .35em;
    }
    #how-it-works p {
        margin-top: 1em;
    }
    #id_505c742a aside {
        max-width: none;
    }
    #id_505c742a li {
        position: relative;
        padding-right: 2.5em;
    }
    #id_505c742a ul.id_505c742a__open {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        padding: 1em;
    }
    #id_505c742a ul.id_505c742a__open::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(var(--background), .8);
        -webkit-backdrop-filter: saturate(180%) blur(20px);
        backdrop-filter: saturate(180%) blur(20px);
        z-index: 1001;
    }
    #id_505c742a ul.id_505c742a__open li {
        position: relative;
        z-index: 1002;
    }
    #id_505c742a ul:not(.id_505c742a__open) li:not(.id_505c742a__active) {
        display: none;
    }
    #id_505c742a ul:not(.id_505c742a__open) li::after {
        content: "";
        position: absolute;
        right: .8em;
        top: 55%;
        transform: translateY(-50%);
        pointer-events: none;
        opacity: .5;
        width: 1.15em;
        height: 1.15em;
        background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' stroke='currentColor'%3E%3Cpath d='M7 9L12 4' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M17 9L12 4' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M7 15L12 20' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M17 15L12 20' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
    }
}
@media (max-width: 490px) {
    #id_505c742a > div {
        margin: 0 1em;
    }
    #id_505c742a h3 {
        font-size: 2em;
    }
}
</style>

<div id="id_505c742a">
    <div id="how-it-works">
        <h3>
            See how Appio works for
            <label id="id_505c742a__select-wrapper" for="id_505c742a__select">
                <select id="id_505c742a__select">
                    <option value="id_505c742a__for-you">You</option>
                    <option value="id_505c742a__for-your-users">Your Users</option>
                </select>
                <span></span>
            </label>
        </h3>
        <p>From setup to mobile engagement, here’s what happens.</p>
    </div>
    <main>
        <aside>
            <ul id="id_505c742a__for-you">
                <li class="id_505c742a__active" data-time="0:00">
                    <h4>
                        <span aria-hidden="true">0:00</span>
                        Set up your brand
                    </h4>
                    <p>
                        Upload your logo, title, and description.
                        Your identity is front and center.
                    </p>
                </li>
                <li data-time="0:40">
                    <h4>
                        <span aria-hidden="true">0:40</span>
                        Install JavaScript snippet
                    </h4>
                    <p>Paste the short snippet into your app and link the <var>user</var> field. That’s it.</p>
                </li>
                <li data-time="0:55">
                    <h4>
                        <span aria-hidden="true">0:55</span>
                        Create a widget
                    </h4>
                    <p>Design your widget and connect it to your live data.</p>
                </li>
                <li data-time="1:20">
                    <h4>
                        <span aria-hidden="true">1:20</span>
                        Send a notification
                    </h4>
                    <p>Use our web app, API, or one of our no-code tool.</p>
                </li>
            </ul>

            <ul id="id_505c742a__for-your-users" style="display:none;">
                <li class="id_505c742a__active" data-time="2:00">
                    <h4>
                        <span aria-hidden="true">2:00</span>
                        Onboard
                    </h4>
                    <p>User taps a link or scans a QR and lands in your branded flow, no signup needed.</p>
                </li>
                <li data-time="2:10">
                    <h4>
                        <span aria-hidden="true">2:10</span>
                        Add the widget
                    </h4>
                    <p>They're guided to add your widget to their home screen.</p>
                </li>
                <li data-time="2:20">
                    <h4>
                        <span aria-hidden="true">2:20</span>
                        Get notified
                    </h4>
                    <p>Instant, interactive and persistent. No spam folders or missed updates.</p>
                </li>
            </ul>
        </aside>
        <div id="id_505c742a__video">
            
            <p aria-hidden="true">Videos coming soon...</p>
        </div>
    </main>
</div>
<noscript>
    <style>
        #id_505c742a {
            animation: fadeIn .5s ease-out .3s forwards
        }
    </style>
</noscript>
<script>
    (function () {
        // Small screen detection
        let isSmall, isOpen = false;
        const mediaQuery = window.matchMedia("(max-width: 990px)")

        function handleViewportChange(e) {
            isSmall = e.matches
        }

        handleViewportChange(mediaQuery);
        mediaQuery.addEventListener("change", handleViewportChange);

        // `For who` and items selection
        const
            rootEl = document.getElementById("id_505c742a"),
            selectWrapper = document.getElementById('id_505c742a__select-wrapper'),
            select = document.querySelector("#id_505c742a__select"),
            selectValue = selectWrapper.querySelector("span"),
            uls = document.querySelectorAll("#id_505c742a__for-you, #id_505c742a__for-your-users"),
            lis = document.querySelectorAll("#id_505c742a__for-you li, #id_505c742a__for-your-users li"),
            activeClass = "id_505c742a__active",
            openClass = "id_505c742a__open";

        selectValue.innerText = select.options[select.selectedIndex].text;
        selectWrapper.classList.add("js");

        function resetActive() {
            lis.forEach(l => {
                l.classList.remove(activeClass);
            });
        }

        function switchTo(sectionId, index, scroll) {
            resetActive();

            const el = document.querySelector(`#${sectionId} li:nth-child(${index + 1})`);
            el.classList.add(activeClass);

            if (sectionId === "id_505c742a__for-you") {
                const
                    margin = getComputedStyle(rootEl).getPropertyValue("--li-margin"),
                    height = el.getBoundingClientRect().height + parseFloat(margin);
                document.querySelector(`#${sectionId}`).style.setProperty("--top-extra", `${height * index}px`);
            }

            // Scroll video to view
            if (scroll && isSmall) {
                rootEl.querySelector("main").scrollIntoView({ behavior: "instant" });
            }

            // Seek and play video
            seekAndPlayVideo(el.dataset.time);
        }

        function pauseVideo() {
            // TODO: pause video
            console.log("pause video");
        }

        function seekAndPlayVideo(time) {
            // TODO: seek and play video
            console.log("seek and play video", time);
        }

        function open(sectionId) {
            isOpen = true;
            document.getElementById(sectionId).classList.add(openClass);
            document.body.style.overflow = "hidden";

            pauseVideo();
        }

        function close(sectionId) {
            isOpen = false;
            document.getElementById(sectionId).classList.remove(openClass);
            document.body.style.overflow = "visible";
        }

        select.addEventListener("change", (e) => {
            uls.forEach(u => {
                u.style.display = "none";
            });
            const el = document.querySelector(`#${e.target.value}`);
            el.style.display = "block";
            switchTo(el.id, 0, false);

            selectValue.innerText = select.options[select.selectedIndex].text;
        });

        select.addEventListener("focus", () => {
            pauseVideo();
        });

        uls.forEach(u => {
            const lis = u.querySelectorAll("li");
            lis.forEach((l, i) => {
                l.index = i;
                l.addEventListener("click", () => {
                    // Fix post-css animation
                    fixCssAnimation()

                    if (isSmall && !isOpen) {
                        open(u.id);
                    } else {
                        switchTo(u.id, l.index, true);
                        close(u.id);
                    }
                });
            });
        });

        function fixCssAnimation() {
            const animatedParent = rootEl.closest(".js-in-viewport");
            if (! animatedParent) return;
            animatedParent.classList.remove("js-in-viewport");
            animatedParent.style.opacity = 1;
        }
    })();
</script>
</section>
<section class="id_093bc9a8__fade-in">
    <style>
#id_3e5d612d {
    margin: var(--section-gap) auto 0;
    display: flex;
    gap: 2em;
    max-width: 40em;
    height: 334px;
    overflow: hidden;
    border-radius: 1.5em;
    box-shadow: var(--box-shadow);
    background: rgb(var(--box-background));
    padding: 1em;
}
#id_3e5d612d img {
    border-radius: .8em;
    height: 300px;
    object-fit: cover;
    object-position: top;
}
#id_3e5d612d h4 {
    margin-top: .5em;
    font-size: 2.5em;
}
#id_3e5d612d__cta {
    display: flex;
    justify-content: space-between;
    align-items: start;
}
#id_3e5d612d a {
    position: relative;
    text-decoration: none;
    color: inherit;
    display: block;
    margin-top: .5em;
    font-weight: 700;
    font-size: 1.2em;
}
#id_3e5d612d a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background-color: currentColor;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}
#id_3e5d612d a:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}
#id_3e5d612d a:not(:hover)::after {
    transform-origin: right;
}
@media (max-width: 700px) {
    #id_3e5d612d {
        height: auto;
        gap: 1.5em;
    }
    #id_3e5d612d h4 {
        margin-top: 0;
    }
}
@media (max-width: 550px) {
    #id_3e5d612d {
        flex-direction: column;
    }
    #id_3e5d612d h4 {
        font-size: 2em;
    }
    #id_3e5d612d img {
        width: 100%;
        height: 100vw;
    }
}}
</style>
<div id="id_3e5d612d">
    <img src="/assets/images/tom.jpg" alt="Tom" width="200" height="200">
    <div>
        <h4>Meet Tom</h4>
        <p>
            Tom runs a SaaS business.<br>
            His users wanted mobile notifications and widgets.<br>
            But building a mobile app would take too long.<br>
            Tom found Appio and delivered both in minutes.<br>
            His users love it.<br>
        </p>
        <div id="id_3e5d612d__cta">
            <a href="/list/">Be like Tom!</a>
        </div>
    </div>
</div>
</section>
<section class="id_093bc9a8__fade-in">
    <style>
#id_e7246548 {
    margin-top: var(--section-gap);
    display: flex;
    align-items: center;
    gap: 2.4em;
}
#id_e7246548 main {
    box-shadow: var(--box-shadow);
    background: rgb(var(--box-background));
    padding: 2em 1.5em;
    border-radius: 1.5em;
}
#id_e7246548 main div {
    display: flex;
    gap: 1em;
    border-bottom: solid 1px rgb(var(--border-color));
    padding-bottom: 1em;
    margin-bottom: 1em;
}
#id_e7246548 main div:last-child {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
}
#id_e7246548 main h3 {
    font-size: 1.2em;
    width: 11.5em;
}
#id_e7246548 main p {
    margin-top: 0;
    line-height: 1.4;
    flex: 1;
}
#id_e7246548 main span {
    color: rgb(1, 89, 1);
    background: rgb(218, 249, 212);
    font-weight: 600;
}
#id_e7246548 aside h2 {
    font-size: 2em;
}
#id_e7246548__signature {
    user-select: none;
}
#id_e7246548__signature svg {
    display: block;
    transform: rotate(-10deg);
    margin: -20px 0 -40px -25px;
    width: 150px;
    height: 150px;
}
#id_e7246548__signature em {
    font-weight: 400;
    font-size: 0;
    transition: font-size 0.1s ease;
}
#id_e7246548__signature:hover em {
    font-size: 1em;
}
@media (max-width: 1150px) {
    #id_e7246548 {
        flex-direction: column-reverse;
    }
    #id_e7246548 aside {
        width: 100%;
        padding: 0 1.5em;
    }
    #id_e7246548 aside h2 {
        font-size: 2.5em;
    }
}
@media (max-width: 690px) {
    #id_e7246548 main div {
        flex-direction: column;
        gap: .5em;
    }
    #id_e7246548 main h3 {
        width: auto;
    }
}
@media (max-width: 490px) {
    #id_e7246548 aside {
        padding: 0 1em
    }
    #id_e7246548 aside h2 {
        font-size: 2em;
    }
}
</style>
<div id="id_e7246548">
    <main>
        <div>
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="13" fill="#005900"></circle><path fill-rule="evenodd" clip-rule="evenodd" d="M3 16C3 8.82 8.82 3 16 3C23.18 3 29 8.82 29 16C29 23.18 23.18 29 16 29C8.82 29 3 23.18 3 16ZM20.8133 13.5813C20.8933 13.4747 20.9512 13.3532 20.9836 13.2239C21.0159 13.0946 21.0221 12.9602 21.0018 12.8285C20.9815 12.6968 20.935 12.5704 20.8651 12.4569C20.7953 12.3434 20.7034 12.245 20.595 12.1675C20.4866 12.09 20.3638 12.035 20.2337 12.0056C20.1037 11.9763 19.9692 11.9732 19.838 11.9966C19.7068 12.02 19.5816 12.0694 19.4697 12.1419C19.3579 12.2144 19.2616 12.3085 19.1867 12.4187L14.872 18.4587L12.7067 16.2933C12.5171 16.1167 12.2664 16.0205 12.0073 16.0251C11.7482 16.0297 11.5011 16.1346 11.3178 16.3178C11.1346 16.5011 11.0297 16.7482 11.0251 17.0073C11.0205 17.2664 11.1167 17.5171 11.2933 17.7067L14.2933 20.7067C14.396 20.8092 14.5197 20.8882 14.656 20.9382C14.7922 20.9881 14.9377 21.0078 15.0824 20.9959C15.227 20.984 15.3673 20.9407 15.4935 20.8691C15.6197 20.7975 15.7289 20.6993 15.8133 20.5813L20.8133 13.5813Z" fill="#DAF9D4"></path></svg>
            <h3>Your users expect mobile features</h3>
            <p>
                They want to access your product from their <span>mobile home screen</span> and receive <span>real-time push notifications</span>.
                Some may even leave for competitors who offer this.
            </p>
        </div>
        <div>
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="13" fill="#005900"></circle><path fill-rule="evenodd" clip-rule="evenodd" d="M3 16C3 8.82 8.82 3 16 3C23.18 3 29 8.82 29 16C29 23.18 23.18 29 16 29C8.82 29 3 23.18 3 16ZM20.8133 13.5813C20.8933 13.4747 20.9512 13.3532 20.9836 13.2239C21.0159 13.0946 21.0221 12.9602 21.0018 12.8285C20.9815 12.6968 20.935 12.5704 20.8651 12.4569C20.7953 12.3434 20.7034 12.245 20.595 12.1675C20.4866 12.09 20.3638 12.035 20.2337 12.0056C20.1037 11.9763 19.9692 11.9732 19.838 11.9966C19.7068 12.02 19.5816 12.0694 19.4697 12.1419C19.3579 12.2144 19.2616 12.3085 19.1867 12.4187L14.872 18.4587L12.7067 16.2933C12.5171 16.1167 12.2664 16.0205 12.0073 16.0251C11.7482 16.0297 11.5011 16.1346 11.3178 16.3178C11.1346 16.5011 11.0297 16.7482 11.0251 17.0073C11.0205 17.2664 11.1167 17.5171 11.2933 17.7067L14.2933 20.7067C14.396 20.8092 14.5197 20.8882 14.656 20.9382C14.7922 20.9881 14.9377 21.0078 15.0824 20.9959C15.227 20.984 15.3673 20.9407 15.4935 20.8691C15.6197 20.7975 15.7289 20.6993 15.8133 20.5813L20.8133 13.5813Z" fill="#DAF9D4"></path></svg>
            <h3>You are exploring mobile options</h3>
            <p>
                You’ve considered building in-house, hiring an agency, or using an app builder.
                
                Appio helps you <span>skip the complexity</span>, <span>cut costs</span>, and <span>launch faster</span>.
            </p>
        </div>
        <div>
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="13" fill="#005900"></circle><path fill-rule="evenodd" clip-rule="evenodd" d="M3 16C3 8.82 8.82 3 16 3C23.18 3 29 8.82 29 16C29 23.18 23.18 29 16 29C8.82 29 3 23.18 3 16ZM20.8133 13.5813C20.8933 13.4747 20.9512 13.3532 20.9836 13.2239C21.0159 13.0946 21.0221 12.9602 21.0018 12.8285C20.9815 12.6968 20.935 12.5704 20.8651 12.4569C20.7953 12.3434 20.7034 12.245 20.595 12.1675C20.4866 12.09 20.3638 12.035 20.2337 12.0056C20.1037 11.9763 19.9692 11.9732 19.838 11.9966C19.7068 12.02 19.5816 12.0694 19.4697 12.1419C19.3579 12.2144 19.2616 12.3085 19.1867 12.4187L14.872 18.4587L12.7067 16.2933C12.5171 16.1167 12.2664 16.0205 12.0073 16.0251C11.7482 16.0297 11.5011 16.1346 11.3178 16.3178C11.1346 16.5011 11.0297 16.7482 11.0251 17.0073C11.0205 17.2664 11.1167 17.5171 11.2933 17.7067L14.2933 20.7067C14.396 20.8092 14.5197 20.8882 14.656 20.9382C14.7922 20.9881 14.9377 21.0078 15.0824 20.9959C15.227 20.984 15.3673 20.9407 15.4935 20.8691C15.6197 20.7975 15.7289 20.6993 15.8133 20.5813L20.8133 13.5813Z" fill="#DAF9D4"></path></svg>
            <h3>You want to move fast without the overhead</h3>
            <p>
                You are focused on <span>validating quickly</span> and <span>avoiding the overhead</span> of app stores and custom development.
            </p>
        </div>
    </main>
    <aside>
        <h2>Who is Appio for</h2>
        <p>We have 3 simple criteria. If these fit you, Appio will feel like a superpower for your team.</p>
        <div>
            <div id="id_e7246548__signature">
                <svg enable-background="new 0 0 1024 1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="m483.755066 569.707275c17.294281-11.710205 32.852661-25.030029 48.407837-38.355163 5.661621-4.849915 6.900451-11.978577 9.450989-18.400513 8.718628-21.952331 17.565918-43.846985 26.940673-65.532349 7.071228-16.356903 14.070801-32.730743 23.148743-48.109252 6.855225-11.61316 13.863098-23.13321 22.157776-33.80362 2.263123-2.911377 4.767456-5.508331 8.309875-6.982696 8.083863-3.364472 16.097901 1.109283 17.151368 9.8656 1.353943 11.255371-.575684 22.203522-3.802796 32.926331-10.825683 35.97052-28.089172 68.520965-51.755066 97.736786-10.697693 13.206451-23.036743 24.917816-33.857971 37.969085-5.37677 6.484742-8.023437 14.481385-10.005005 23.57782 3.700867-1.117798 5.120301-3.192566 6.591675-5.115417 10.24707-13.390991 21.903015-25.480103 33.989075-37.195801 3.811218-3.694397 7.347473-7.65921 11.24591-11.295349 4.638611-4.326569 9.982239-5.049774 15.752015-4.106903 2.669128.436126 4.004516 2.677429 4.814636 5.220916 1.775268 5.573914.070129 10.818909-1.238953 16.151612-2.807129 11.435547-5.957153 22.82074-5.76123 34.761902.149902 9.13977 2.618774 10.71643 10.972656 6.664306 4.184692-2.029846 8.196838-4.414245 12.296936-6.619934 3.064758-1.648803 4.879211-4.089416 6.141357-7.435974 8.260315-21.902405 21.097168-40.750122 38.832703-56.067291 5.141663-4.440552 10.868408-8.330567 17.709473-9.899475 12.224853-2.80365 19.453064 1.362854 23.063049 13.360382.981567 3.262085 2.152954 6.074402 5.173767 8.149933 3.729187 2.562317 4.056336 6.530609 3.256775 10.732513-1.827515 9.603516-7.339966 17.800476-10.203674 27.011353-1.285523 4.13446-2.771668 8.216735-3.127625 12.550842-.17157 2.089539-.633667 4.606811 1.640747 5.903259 2.213684 1.261841 4.47876.435303 6.647339-.661743 7.911621-4.002441 14.715698-9.724121 22.275147-14.268433 15.001159-9.017883 24.409973-20.613342 26.57135-39.471832 2.483276-21.667755 13.277099-41.33194 21.593078-61.461731 8.149109-19.725952 17.902771-38.668701 28.167298-57.342743 8.838745-16.080383 18.910705-31.397949 30.782959-45.422149 4.516967-5.335724 9.894958-9.7612 16.468383-12.705017 8.960022-4.012604 17.334107-.273652 19.822632 9.117187 2.908387 10.975281.019776 21.40799-2.947998 31.772736-8.044189 28.093078-21.436096 53.694885-36.900757 78.309723-13.123474 20.888366-27.629882 40.739379-43.178711 59.913421-7.339172 9.050354-14.924987 17.86792-22.436279 26.759644-1.202148 1.423156-2.467529 2.878723-2.46698 4.784973.004944 16.182312 7.682373 34.315918 28.797119 34.077453 2.147034-.024292 4.332093.129944 6.437134.529786 1.84082.34967 4.225586.257812 4.513184 3.036865.270385 2.612915-.904969 4.500061-3.06482 5.828857-6.70288 4.123719-14.097717 4.094178-21.288513 2.490174-14.76123-3.292359-23.26123-13.351258-27.625915-27.36438-.875977-2.812378-.966675-5.895508-3.112-8.869568-5.56311 4.327087-10.900207 8.734985-16.502869 12.774536-7.94049 5.725098-15.499511 12.243835-25.419982 14.459961-8.306458 1.855652-15.925293.907654-21.698365-6.220398-2.741638-3.385132-3.51477-7.419861-3.822509-11.633972-.188233-2.578919.721679-5.315979-1.048096-8.002686-3.242249 1.247376-5.350769 3.901673-7.57843 6.141907-5.532349 5.56366-11.370056 10.725525-17.549317 15.554932-4.46051 3.486145-9.56726 5.527465-14.917297 6.617798-6.023193 1.227417-11.766663.087646-15.431397-5.574769-2.463745-3.806823-5.144165-3.50354-8.294677-1.142272-1.199707.899169-2.417298 1.77832-3.665284 2.608764-7.660156 5.097351-15.694885 7.340881-24.444519 2.858887-8.382812-4.293945-9.351745-12.445496-9.966186-20.440308-.639893-8.325561.66333-16.583923 2.386597-24.735351.415588-1.966248.642395-3.826294.096496-6.065003-3.990967 1.283692-5.660217 4.516419-8.111938 6.848389-13.821228 13.145752-25.132141 28.39209-36.631165 43.490112-3.023193 3.969422-6.155151 7.87323-9.481079 11.588806-2.221924 2.4823-4.894287 4.507569-8.482239 4.793763-6.346679.506164-10.188781-2.562195-9.771545-8.883973.468445-7.09851 1.894165-14.130004 2.74762-21.209472.218872-1.816223 1.713073-4.066773-.671265-5.467041-2.297302-1.349182-3.685303.675232-5.154297 1.909607-12.113892 10.179626-24.397369 20.123779-38.435944 27.616577-5.58493 2.980896-11.246063 5.79956-17.552856 6.997314-19.98587 3.795533-31.94693-12.327942-30.270569-28.914551 1.782715-17.638977 9.739594-32.733764 18.957214-47.258789 8.408478-13.249939 18.229279-25.440246 29.674195-36.273193 5.914001-5.597717 12.524414-10.016815 20.426452-12.040008 6.462952-1.654755 12.162262-.015229 16.547089 5.359283 4.736389 5.805572 5.7995 12.450592 3.371277 19.238342-2.403198 6.717621-5.720825 13.11319-8.733185 19.605591-1.479004 3.187683-4.169708 5.088745-7.374969 6.388123-2.10907.854919-4.168702 1.420776-6.089386-.254761-2.032624-1.773132-3.005341-4.018128-2.243653-6.713745 1.586396-5.614197 3.963166-10.926056 6.216706-16.294129 1.143615-2.724121 4.039947-6.021057 1.059082-8.612487-3.273713-2.84607-5.886261.980987-8.081513 2.754943-23.093231 18.661377-40.520965 41.180054-47.286774 70.781739-.111328.48706-.275085.965454-.346954 1.457763-1.624817 11.12909 1.079315 19.811463 16.39798 14.509766 7.540771-2.609863 14.046173-6.763367 20.828186-11.336121m337.933655-112.483276c4.946106-7.497101 10.012451-14.91806 14.815612-22.505646 14.332886-22.641815 25.892823-46.502075 30.783997-73.093109.70105-3.811249 2.823608-7.898315.476929-11.884521-2.671387-.064759-4.414673 1.164856-5.891541 2.829284-5.079834 5.725281-10.567383 11.152314-15.11322 17.276673-17.54425 23.636444-30.966064 49.633514-42.477173 76.620544-5.410217 12.684021-10.219787 25.629944-15.075439 38.542206-2.23584 5.945496-3.928284 12.095306-6.054383 18.737732 1.901856-.166351 3.023011.080139 3.431397-.350037 13.223205-13.926788 23.250183-30.292816 35.103821-46.173126m-125.393372 67.727478c-2.455749-4.981018-1.468567-9.441894 1.700684-13.82074 1.758362-2.42929 2.337524-5.330352-.340699-7.645355-2.555297-2.208649-5.081787-.738129-7.323913.715576-20.218567 13.108429-33.148621 31.741547-40.846192 54.27298-.653259 1.912109-2.108886 4.249329.412537 5.8573 2.085693 1.330017 4.136047.433227 5.994934-.924622 14.957825-10.925964 28.303345-23.562012 40.402649-38.455139m-119.511902-41.672058c20.299439-27.151489 36.088135-56.564484 44.826721-89.503204.46521-1.753571.772767-3.560822.995057-5.362671.142395-1.154389.078979-2.451935-1.317017-2.910157-1.28479-.421783-2.256958.325898-3.03009 1.252198-.74347.890747-1.424378 1.838836-2.076355 2.800018-10.754151 15.855103-19.301453 32.938355-27.725098 50.079834-7.554687 15.373261-13.156555 31.574921-19.762329 47.351776-.539856 1.289429-1.267517 2.8078.553711 4.222168 2.972107-1.859375 4.654907-4.996063 7.5354-7.929962z"/><path d="m420.276611 480.75824c2.749695 7.104065-.27951 13.11499-2.767456 18.994079-8.050323 19.023377-15.146698 38.363404-20.461761 58.334778-.854218 3.209717-1.493439 6.477661-2.194123 9.726929-1.423461 6.600769-.758514 7.541504 5.805054 8.331177 1.891846 7.443969-4.20047 17.493591-11.674988 18.673584-4.264953.673217-7.121276-1.768921-9.051147-5.543885-3.707764-7.252502-2.205078-14.714721-.66095-21.991272 2.204254-10.387085 4.147461-20.862182 7.761933-30.91632.930297-2.587708 2.197784-5.241456 1.172546-9.057557-4.159271 2.903138-6.545319 6.857789-9.314819 10.359864-13.732331 17.364624-26.929596 35.217773-44.697754 48.871948-3.023011 2.32312-6.258759 4.450623-9.629151 6.22644-14.579651 7.681824-27.601837.144898-27.834442-16.28479-.210968-14.903564 2.830139-29.382934 6.602356-43.738769 6.728241-25.605744 15.250427-50.625244 24.328034-75.469818 1.821502-4.985199 3.602111-9.985352 5.401641-14.978546.5094-1.413422 1.104462-2.830597-.558105-4.587189-5.612274 1.682922-8.976776 6.45694-12.377228 10.692077-21.819366 27.175751-41.629944 55.774262-59.04776 85.966889-7.229172 12.53125-13.464126 25.633667-20.591034 38.227417-3.731384 6.593689-6.62529 13.84198-12.815948 18.857422-4.920029 3.986206-9.168411 5.015442-13.194824 2.760864-4.121964-2.308044-5.880768-7.238464-5.264466-13.013489 1.803879-16.903137 8.11734-32.514709 13.667664-48.329773 12.893112-36.73764 28.572433-72.40094 42.238495-108.834472 8.535614-22.755768 18.627624-44.87262 26.893097-67.721863.396515-1.0961.837463-2.177002 1.205871-3.282257.472687-1.418274 1.25119-3.107361-.147583-4.183869-1.801208-1.3862-3.093841.283203-4.19281 1.461884-10.105743 10.838959-19.916412 21.966797-28.152496 34.295807-13.96257 20.901275-27.275192 42.215057-39.264038 64.337433-14.118454 26.052002-27.990189 52.21878-40.661713 79.02182-13.871368 29.341003-27.641006 58.707336-37.988831 89.534485-1.745437 5.199707-3.450531 10.413757-5.103027 15.643676-1.269394 4.017517-1.640716 7.854248-.11618 12.146363 2.458557 6.921814-5.497177 15.850341-12.688439 14.582275-4.315284-.761047-7.395713-3.120361-7.206833-8.092712.410027-10.79419 3.788773-20.945008 7.402428-30.92096 15.78299-43.571106 35.277603-85.490478 56.31604-126.757965 15.435333-30.276916 31.5336-60.166534 49.084015-89.252167 14.721329-24.397094 30.414749-48.149963 50.34523-68.759094 5.145264-5.320434 10.854157-9.892242 18.203461-11.768616 7.6315-1.948364 12.411468 1.192292 14.484833 8.901123 2.186096 8.127686-.417939 15.593476-3.361908 22.778962-11.354523 27.71347-23.259308 55.202026-34.552338 82.939972-14.110718 34.658691-28.27211 69.307678-40.246658 104.789794-.488587 1.447754-1.857254 2.96283-.238907 4.702942 2.857452.306702 3.395691-2.219055 4.38446-3.932983 7.819763-13.554199 14.962311-27.539002 23.428772-40.67218 18.588104-28.833893 37.26059-57.678559 62.053528-81.840698 3.59314-3.501709 7.338318-6.756989 11.892151-9.016327 4.875488-2.418976 9.648529-2.54538 14.116608.65448 4.955627 3.549041 5.191192 8.788482 3.795318 13.989257-2.541473 9.468872-5.768341 18.733216-9.240112 27.911194-8.298981 21.939209-16.651032 43.864777-23.445007 66.332978-4.614594 15.260864-8.600831 30.673584-9.771271 46.662109-.121338 1.657654-.102783 3.334534-.0224 4.996338.252167 5.213989 3.036926 6.525207 7.580414 3.731811 11.214599-6.894958 20.009399-16.425415 28.751129-26.05426 18.709442-20.608154 34.615784-43.482727 52.506164-64.751251 2.157929-2.56543 4.451905-4.97876 7.266388-6.801727 4.028504-2.609314 8.00882-3.261933 11.848877 1.116638z"/><path d="m417.664825 419.997681c-1.629852-3.405304-.573333-6.085266 1.718995-7.932495 2.64862-2.134308 5.937164-1.897522 8.975402-.598999 1.809479.773345 3.462922 1.832519 5.37024 2.507812 3.02185 1.069916 7.041168 1.905029 6.342071 6.349945-.659179 4.190918-3.376129 7.179962-7.696686 8.161865-5.184204 1.178131-10.944397-2.107208-14.710022-8.488128z"/></svg>
                <strong>Micha<em>e</em>l Gondar</strong>
                <p>Appio CEO</p>
            </div>
        </div>
    </aside>
</div>
</section>
<section class="id_093bc9a8__fade-in">
    <style>
#id_47275eaa {
    --li-margin: 10px;
    margin: var(--section-gap) auto 0;
    display: flex;
    gap: 1em;
}
#id_47275eaa aside {
    max-width: 21em; /* firefox and safari */
    font-size: .9em;
}
#id_47275eaa h3 {
    font-size: 2em;
    margin-top: 1em;
    padding: 0 .768em;
}
#id_47275eaa ul,
#id_47275eaa li
{
    list-style: none;
    margin: 0;
    padding: 0;
}
#id_47275eaa ul {
    margin-top: 1.12em;
    position: relative;
}
#id_47275eaa li {
    transition-property: all;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-duration: .2s;
    border-radius: 1em;
    padding: 1.2em 1em 1em 1.5em;
    cursor: pointer;
    margin-bottom: var(--li-margin);
}
#id_47275eaa li:hover {
    opacity: .8;
    background-color: rgb(var(--box-background));
}
#id_47275eaa li h4 {
    font-size: 1.1em;
    font-weight: 700;
}
#id_47275eaa li h4 span {
    color: rgb(110,110,110);
    margin-right: .3em;
}
#id_47275eaa li p {
    margin-top: .3em;
}
#id_47275eaa li.id_47275eaa__active {
    background: rgb(var(--box-background));
    box-shadow: var(--box-shadow);
}
#id_47275eaa li.id_47275eaa__active h4 {
    color: rgb(var(--btn-color));
}
#id_47275eaa main {
    flex: 1;
    border-radius: 1.5em;
    background: rgb(var(--box-background));
    border-color: #fafafa;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    justify-content: center;
    aspect-ratio: 16 / 9;
    overflow: hidden;
}
#id_47275eaa main p {
    font-size: 3em;
    opacity: .3;
}

@media (max-width: 990px) {
    #id_47275eaa {
        flex-direction: column;
        gap: calc(1em - var(--li-margin));
    }
    #id_47275eaa aside {
        max-width: none;
    }
    #id_47275eaa h3 {
        margin-top: 0;
        font-size: 2.5em;
        /*padding: 0 .667em;*/
        padding: 0 .578em;
    }
    #id_47275eaa ul {
        scroll-margin-top: var(--scroll-margin-top);
        margin-top: 1.67em;
    }
    #id_47275eaa li {
        position: relative;
        padding-right: 2.5em;
    }
    #id_47275eaa ul.id_47275eaa__open {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        padding: 1em;
        margin: 0;
    }
    #id_47275eaa ul.id_47275eaa__open::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(var(--background), .8);
        -webkit-backdrop-filter: saturate(180%) blur(20px);
        backdrop-filter: saturate(180%) blur(20px);
        z-index: 1001;
    }
    #id_47275eaa ul.id_47275eaa__open li {
        position: relative;
        z-index: 1002;
    }
    #id_47275eaa ul:not(.id_47275eaa__open) li:not(.id_47275eaa__active) {
        display: none;
    }
    #id_47275eaa ul:not(.id_47275eaa__open) li::after {
        content: "";
        position: absolute;
        right: .8em;
        top: 55%;
        transform: translateY(-50%);
        pointer-events: none;
        opacity: .5;
        width: 1.15em;
        height: 1.15em;
        background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' stroke='currentColor'%3E%3Cpath d='M7 9L12 4' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M17 9L12 4' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M7 15L12 20' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M17 15L12 20' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
    }
}
@media (max-width: 490px) {
    #id_47275eaa h3 {
        padding: 0 .556em;
        font-size: 2em;
    }
}
</style>
<div id="id_47275eaa">
    <aside>
        <h3>3 things you will achieve in 1 day</h3>
        <ul id="id_47275eaa__list">
            <li class="id_47275eaa__active">
                <h4>Launch your first widget</h4>
                <p>Designed with our simple WYSIWYG editor.</p>
            </li>
            <li>
                <h4>Onboard your first users</h4>
                <p>And let them experience the power of Appio.</p>
            </li>
            <li>
                <h4>Send your first push notification</h4>
                <p>Via our web app.</p>
            </li>
        </ul>
    </aside>
    <main>
        
        <p aria-hidden="true">Screenshots coming soon...</p>
    </main>
</div>
<script>
    (function () {
        // Small screen detection
        let isSmall, isOpen = false;
        const mediaQuery = window.matchMedia("(max-width: 990px)")

        function handleViewportChange(e) {
            isSmall = e.matches
        }

        handleViewportChange(mediaQuery);
        mediaQuery.addEventListener("change", handleViewportChange);

        // Item selection
        const
            rootEl = document.getElementById("id_47275eaa"),
            listEl = document.getElementById("id_47275eaa__list"),
            lis = document.querySelectorAll("#id_47275eaa li"),
            activeClass = "id_47275eaa__active",
            openClass = "id_47275eaa__open";

        function resetActive() {
            lis.forEach(l => {
                l.classList.remove(activeClass);
            });
        }

        function switchTo(index) {
            resetActive();
            const el = document.querySelector(`#id_47275eaa li:nth-child(${index + 1})`);
            el.classList.add(activeClass);

            switchImageTo(index);

            if (isSmall) {
                // Since we are scrolling to the element that is being manipulated, we need to wait for the next frame
                setTimeout(() => {
                    listEl.scrollIntoView({behavior: "instant"});
                }, 1);
            }
        }

        function switchImageTo(index) {
            // TODO: switch image
            console.log("switch image to:", index);
        }

        function open() {
            isOpen = true;
            listEl.classList.add(openClass);
            document.body.style.overflow = "hidden";
        }

        function close() {
            isOpen = false;
            listEl.classList.remove(openClass);
            document.body.style.overflow = "visible";
        }

        lis.forEach((l, i) => {
            l.addEventListener("click", () => {
                // Fix post-css animation
                fixCssAnimation()

                if (isSmall && !isOpen) {
                    open();
                } else {
                    switchTo(i);
                    close();
                }
            });
        });

        function fixCssAnimation() {
            const animatedParent = rootEl.closest(".js-in-viewport");
            if (! animatedParent) return;
            animatedParent.classList.remove("js-in-viewport");
            animatedParent.style.opacity = 1;
        }
    })();
</script>
</section>
<section class="id_093bc9a8__fade-in">
    <style>
#id_5d1266eb {
    margin: var(--section-gap) auto 0;
    text-align: center;
}
#id_5d1266eb h2 {
    font-size: 4em;
    line-height: 1.3em;
}
#id_5d1266eb h2 span {
    padding: 0 .15em;
    border-radius: .2em;
}
#id_5d1266eb h2 span.red {
    color: rgb(128, 0, 0);
    background: rgb(255, 210, 225);
}
#id_5d1266eb h2 span.green {
    color: rgb(1, 89, 1);
    background: rgb(218, 249, 212);
}
#id_5d1266eb h2 span.blue {
    color: rgb(0, 61, 90);
    background: rgb(201, 240, 255);
}
#id_5d1266eb a {
    margin-top: 1.5em;
    display: inline-block;
    text-decoration: none;
    padding: .8em 1.5em;
    border-radius: 1.5em;
    font-weight: 700;
    font-size: 1.4em;
    user-select: none;
    background: rgb(var(--btn-color));
    color: #fff;
    box-shadow: rgba(0, 0, 0, 0.1) 0 1px 1px 0, rgba(0, 0, 0, 0.08) 0 2px 3px 0, rgba(0, 0, 0, 0.12) 0 4px 8px 0, rgba(6, 54, 109, 0.4) 0 -3px 2px 0 inset, rgba(255, 255, 255, 0.14) 0 2px .4px 0 inset;
}
#id_5d1266eb a:hover {
    background: rgb(var(--btn-hover-color));
}
#id_5d1266eb a:focus {
    box-shadow: none;
}
@media (max-width: 1300px) {
    #id_5d1266eb h2 {
        font-size: 3em;
    }
}
@media (max-width: 490px) {
    #id_5d1266eb h2 {
        font-size: 1.6em;
    }
}
</style>
<div id="id_5d1266eb">
    <h2>
        Appio is <span class="red">not</span> another app builder.<br>
        It’s <span class="blue">mobile</span>, <span class="green">simplified</span>.
    </h2>
    <a href="/list/">Let’s get started</a>
</div>
</section>
<section class="id_093bc9a8__fade-in">
    <style>
#id_7e1b2c2c {
    margin: var(--section-gap) auto 0;
}
#id_7e1b2c2c__header {
    margin: 0 1.3em;
}
#id_7e1b2c2c h2 {
    font-size: 3em;
}
#id_7e1b2c2c__benefits {
    margin-top: 1.5em;
    display: flex;
    gap: 1.5em;
}
#id_7e1b2c2c main {
    box-shadow: var(--box-shadow);
    background: rgb(var(--box-background));
    padding: 2em 1.5em;
    border-radius: 1.5em;
}
#id_7e1b2c2c main svg {
    display: block;
    border-radius: 50%;
}
#id_7e1b2c2c main div {
    display: flex;
    gap: 1em;
    border-bottom: solid 1px rgb(var(--border-color));
    padding-bottom: 1em;
    margin-bottom: 1em;
}
#id_7e1b2c2c main div:last-child {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
}
#id_7e1b2c2c main h3 {
    font-size: 1.2em;
    width: 11em;
    padding-top: .15em;
}
#id_7e1b2c2c main p {
    margin-top: 0;
    line-height: 1.4;
    flex: 1;
}
@media (max-width: 1300px) {
    #id_7e1b2c2c main div {
        flex-direction: column;
        gap: .5em;
    }
    #id_7e1b2c2c main h3 {
        width: auto;
        padding-top: 0;
    }
}
@media (max-width: 1150px) {
    #id_7e1b2c2c h2 {
        font-size: 2.5em;
    }
}
@media (max-width: 890px) {
    #id_7e1b2c2c__benefits {
        flex-direction: column;
        gap: 0;
    }
    #id_7e1b2c2c main:first-of-type {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        padding-bottom: 1.5em;
    }
    #id_7e1b2c2c main:last-of-type {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        padding-top: 1.5em;
    }
}
@media (max-width: 790px) {
    #id_7e1b2c2c h2 {
        font-size: 2em;
    }
}
@media (max-width: 490px) {
    #id_7e1b2c2c__header {
        margin: 0 1em;
    }
}
</style>
<div id="id_7e1b2c2c">
    <div id="id_7e1b2c2c__header">
        <h2>Why teams choose Appio</h2>
        <p>Mobile features, minus the mobile complexity.</p>
    </div>
    <div id="id_7e1b2c2c__benefits">
        <main>
            <div>
                <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="13" fill="#005900"></circle><path fill-rule="evenodd" clip-rule="evenodd" d="M3 16C3 8.82 8.82 3 16 3C23.18 3 29 8.82 29 16C29 23.18 23.18 29 16 29C8.82 29 3 23.18 3 16ZM20.8133 13.5813C20.8933 13.4747 20.9512 13.3532 20.9836 13.2239C21.0159 13.0946 21.0221 12.9602 21.0018 12.8285C20.9815 12.6968 20.935 12.5704 20.8651 12.4569C20.7953 12.3434 20.7034 12.245 20.595 12.1675C20.4866 12.09 20.3638 12.035 20.2337 12.0056C20.1037 11.9763 19.9692 11.9732 19.838 11.9966C19.7068 12.02 19.5816 12.0694 19.4697 12.1419C19.3579 12.2144 19.2616 12.3085 19.1867 12.4187L14.872 18.4587L12.7067 16.2933C12.5171 16.1167 12.2664 16.0205 12.0073 16.0251C11.7482 16.0297 11.5011 16.1346 11.3178 16.3178C11.1346 16.5011 11.0297 16.7482 11.0251 17.0073C11.0205 17.2664 11.1167 17.5171 11.2933 17.7067L14.2933 20.7067C14.396 20.8092 14.5197 20.8882 14.656 20.9382C14.7922 20.9881 14.9377 21.0078 15.0824 20.9959C15.227 20.984 15.3673 20.9407 15.4935 20.8691C15.6197 20.7975 15.7289 20.6993 15.8133 20.5813L20.8133 13.5813Z" fill="#DAF9D4"></path></svg>
                <h3>Set up in minutes</h3>
                <p>No mobile team, no designing the app.</p>
            </div>
            <div>
                <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="13" fill="#005900"></circle><path fill-rule="evenodd" clip-rule="evenodd" d="M3 16C3 8.82 8.82 3 16 3C23.18 3 29 8.82 29 16C29 23.18 23.18 29 16 29C8.82 29 3 23.18 3 16ZM20.8133 13.5813C20.8933 13.4747 20.9512 13.3532 20.9836 13.2239C21.0159 13.0946 21.0221 12.9602 21.0018 12.8285C20.9815 12.6968 20.935 12.5704 20.8651 12.4569C20.7953 12.3434 20.7034 12.245 20.595 12.1675C20.4866 12.09 20.3638 12.035 20.2337 12.0056C20.1037 11.9763 19.9692 11.9732 19.838 11.9966C19.7068 12.02 19.5816 12.0694 19.4697 12.1419C19.3579 12.2144 19.2616 12.3085 19.1867 12.4187L14.872 18.4587L12.7067 16.2933C12.5171 16.1167 12.2664 16.0205 12.0073 16.0251C11.7482 16.0297 11.5011 16.1346 11.3178 16.3178C11.1346 16.5011 11.0297 16.7482 11.0251 17.0073C11.0205 17.2664 11.1167 17.5171 11.2933 17.7067L14.2933 20.7067C14.396 20.8092 14.5197 20.8882 14.656 20.9382C14.7922 20.9881 14.9377 21.0078 15.0824 20.9959C15.227 20.984 15.3673 20.9407 15.4935 20.8691C15.6197 20.7975 15.7289 20.6993 15.8133 20.5813L20.8133 13.5813Z" fill="#DAF9D4"></path></svg>
                <h3>Save money</h3>
                <p>Avoid hiring developers, agencies, and fighting app store headaches.</p>
            </div>
            <div>
                <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="13" fill="#005900"></circle><path fill-rule="evenodd" clip-rule="evenodd" d="M3 16C3 8.82 8.82 3 16 3C23.18 3 29 8.82 29 16C29 23.18 23.18 29 16 29C8.82 29 3 23.18 3 16ZM20.8133 13.5813C20.8933 13.4747 20.9512 13.3532 20.9836 13.2239C21.0159 13.0946 21.0221 12.9602 21.0018 12.8285C20.9815 12.6968 20.935 12.5704 20.8651 12.4569C20.7953 12.3434 20.7034 12.245 20.595 12.1675C20.4866 12.09 20.3638 12.035 20.2337 12.0056C20.1037 11.9763 19.9692 11.9732 19.838 11.9966C19.7068 12.02 19.5816 12.0694 19.4697 12.1419C19.3579 12.2144 19.2616 12.3085 19.1867 12.4187L14.872 18.4587L12.7067 16.2933C12.5171 16.1167 12.2664 16.0205 12.0073 16.0251C11.7482 16.0297 11.5011 16.1346 11.3178 16.3178C11.1346 16.5011 11.0297 16.7482 11.0251 17.0073C11.0205 17.2664 11.1167 17.5171 11.2933 17.7067L14.2933 20.7067C14.396 20.8092 14.5197 20.8882 14.656 20.9382C14.7922 20.9881 14.9377 21.0078 15.0824 20.9959C15.227 20.984 15.3673 20.9407 15.4935 20.8691C15.6197 20.7975 15.7289 20.6993 15.8133 20.5813L20.8133 13.5813Z" fill="#DAF9D4"></path></svg>
                <h3>Increase engagement</h3>
                <p>Via push notifications and home screen widgets.</p>
            </div>
        </main>
        <main>
            <div>
                <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="13" fill="#005900"></circle><path fill-rule="evenodd" clip-rule="evenodd" d="M3 16C3 8.82 8.82 3 16 3C23.18 3 29 8.82 29 16C29 23.18 23.18 29 16 29C8.82 29 3 23.18 3 16ZM20.8133 13.5813C20.8933 13.4747 20.9512 13.3532 20.9836 13.2239C21.0159 13.0946 21.0221 12.9602 21.0018 12.8285C20.9815 12.6968 20.935 12.5704 20.8651 12.4569C20.7953 12.3434 20.7034 12.245 20.595 12.1675C20.4866 12.09 20.3638 12.035 20.2337 12.0056C20.1037 11.9763 19.9692 11.9732 19.838 11.9966C19.7068 12.02 19.5816 12.0694 19.4697 12.1419C19.3579 12.2144 19.2616 12.3085 19.1867 12.4187L14.872 18.4587L12.7067 16.2933C12.5171 16.1167 12.2664 16.0205 12.0073 16.0251C11.7482 16.0297 11.5011 16.1346 11.3178 16.3178C11.1346 16.5011 11.0297 16.7482 11.0251 17.0073C11.0205 17.2664 11.1167 17.5171 11.2933 17.7067L14.2933 20.7067C14.396 20.8092 14.5197 20.8882 14.656 20.9382C14.7922 20.9881 14.9377 21.0078 15.0824 20.9959C15.227 20.984 15.3673 20.9407 15.4935 20.8691C15.6197 20.7975 15.7289 20.6993 15.8133 20.5813L20.8133 13.5813Z" fill="#DAF9D4"></path></svg>
                <h3>Unlock new revenue</h3>
                <p>Upsell new features in your higher-tier plans and attract mobile customers.</p>
            </div>
            <div>
                <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="13" fill="#005900"></circle><path fill-rule="evenodd" clip-rule="evenodd" d="M3 16C3 8.82 8.82 3 16 3C23.18 3 29 8.82 29 16C29 23.18 23.18 29 16 29C8.82 29 3 23.18 3 16ZM20.8133 13.5813C20.8933 13.4747 20.9512 13.3532 20.9836 13.2239C21.0159 13.0946 21.0221 12.9602 21.0018 12.8285C20.9815 12.6968 20.935 12.5704 20.8651 12.4569C20.7953 12.3434 20.7034 12.245 20.595 12.1675C20.4866 12.09 20.3638 12.035 20.2337 12.0056C20.1037 11.9763 19.9692 11.9732 19.838 11.9966C19.7068 12.02 19.5816 12.0694 19.4697 12.1419C19.3579 12.2144 19.2616 12.3085 19.1867 12.4187L14.872 18.4587L12.7067 16.2933C12.5171 16.1167 12.2664 16.0205 12.0073 16.0251C11.7482 16.0297 11.5011 16.1346 11.3178 16.3178C11.1346 16.5011 11.0297 16.7482 11.0251 17.0073C11.0205 17.2664 11.1167 17.5171 11.2933 17.7067L14.2933 20.7067C14.396 20.8092 14.5197 20.8882 14.656 20.9382C14.7922 20.9881 14.9377 21.0078 15.0824 20.9959C15.227 20.984 15.3673 20.9407 15.4935 20.8691C15.6197 20.7975 15.7289 20.6993 15.8133 20.5813L20.8133 13.5813Z" fill="#DAF9D4"></path></svg>
                <h3>Stay on-brand</h3>
                <p>Maintain your logo and design across the whole user experience.</p>
            </div>
            <div>
                <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="13" fill="#005900"></circle><path fill-rule="evenodd" clip-rule="evenodd" d="M3 16C3 8.82 8.82 3 16 3C23.18 3 29 8.82 29 16C29 23.18 23.18 29 16 29C8.82 29 3 23.18 3 16ZM20.8133 13.5813C20.8933 13.4747 20.9512 13.3532 20.9836 13.2239C21.0159 13.0946 21.0221 12.9602 21.0018 12.8285C20.9815 12.6968 20.935 12.5704 20.8651 12.4569C20.7953 12.3434 20.7034 12.245 20.595 12.1675C20.4866 12.09 20.3638 12.035 20.2337 12.0056C20.1037 11.9763 19.9692 11.9732 19.838 11.9966C19.7068 12.02 19.5816 12.0694 19.4697 12.1419C19.3579 12.2144 19.2616 12.3085 19.1867 12.4187L14.872 18.4587L12.7067 16.2933C12.5171 16.1167 12.2664 16.0205 12.0073 16.0251C11.7482 16.0297 11.5011 16.1346 11.3178 16.3178C11.1346 16.5011 11.0297 16.7482 11.0251 17.0073C11.0205 17.2664 11.1167 17.5171 11.2933 17.7067L14.2933 20.7067C14.396 20.8092 14.5197 20.8882 14.656 20.9382C14.7922 20.9881 14.9377 21.0078 15.0824 20.9959C15.227 20.984 15.3673 20.9407 15.4935 20.8691C15.6197 20.7975 15.7289 20.6993 15.8133 20.5813L20.8133 13.5813Z" fill="#DAF9D4"></path></svg>
                <h3>Stand out from your competitors</h3>
                <p>Lead technological advancement in your field.</p>
            </div>
        </main>
    </div>
</div>
</section>
<section class="id_093bc9a8__fade-in">
    <style>
#id_9d02c2c6 {
    margin: var(--section-gap) auto 0;
}
#id_9d02c2c6 h3 {
    text-align: center;
    font-size: 3em;
}
#id_9d02c2c6__features {
    margin-top: 2.5em;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    /*grid-template-rows: repeat(2, auto);*/
    gap: 1.5em;
}
#id_9d02c2c6__features div {
    background: rgb(var(--box-background));
    box-shadow: var(--box-shadow);
    padding: 1.5em 2em;
    border-radius: 1em;
}
#id_9d02c2c6__features div:hover {
    transform: translateY(-.1em);
    box-shadow: var(--box-shadow), rgba(13, 19, 27, 0.1) 0 10px 30px 0;
    transition: box-shadow 0.2s ease, transform 0.2s ease;
}
#id_9d02c2c6__features svg {
    display: block;
    width: 2em;
    height: 2em;
    margin-bottom: .75em;
}
#id_9d02c2c6__features span {
    display: block;
    margin-bottom: .5em;
    font-size: 1.5em;
}
#id_9d02c2c6__features h4 {
    font-weight: 700;
    font-size: 1.3em;
}
@media (max-width: 1300px) {
    #id_9d02c2c6__features {
        grid-template-columns: repeat(3, 1fr);
    }
}
@media (max-width: 990px) {
    #id_9d02c2c6__features {
        grid-template-columns: repeat(2, 1fr);
    }
}
@media (max-width: 590px) {
    #id_9d02c2c6 h3 {
        font-size: 2em;
        text-align: left;
        margin: 0 .5em;
    }
    #id_9d02c2c6__features {
        margin-top: 1.5em;
        grid-template-columns: repeat(1, 1fr);
    }
    
    
    
    
}
</style>
<div id="id_9d02c2c6">

    <h3>What is included</h3>

    <div id="id_9d02c2c6__features">
        <div>
            <svg viewBox="0 0 23.916 24.209" xmlns="http://www.w3.org/2000/svg"><g fill-opacity=".85"><path d="m14.4844 4.67773h-6.39846c-1.21094 0-2.16797.35157-2.77344.95704-.63477.625-.9668 1.5625-.9668 2.79296v7.35347c0 1.2305.33203 2.168.9668 2.793.61523.6153 1.5625.957 2.77344.957h7.37306c1.2207 0 2.168-.3515 2.7832-.957.6348-.625.9668-1.5625.9668-2.793v-6.34811c.5506-.00067 1.081-.09888 1.5722-.28297v6.63108c0 1.709-.4785 3.0469-1.3867 3.9454-.8789.8789-2.2168 1.3769-3.9355 1.3769h-7.37306c-1.70899 0-3.04688-.4883-3.93555-1.3769-.9082-.8985-1.37695-2.2364-1.37695-3.9454v-7.35347c0-1.70898.47851-3.04687 1.37695-3.94531.87891-.8789 2.22656-1.37695 3.93555-1.37695h6.68826c-.1862.49019-.287 1.02036-.2898 1.57226z"/><path d="m19.1992 8.11523c1.8457 0 3.3789-1.5332 3.3789-3.38867s-1.5332-3.38867-3.3789-3.38867c-1.8652 0-3.3887 1.5332-3.3887 3.38867s1.5235 3.38867 3.3887 3.38867z"/></g></svg>
            <h4>Push notifications</h4>
            <p>Real-time delivery across iOS and Android. No native app required.</p>
        </div>
        <div>
            <svg viewBox="0 0 18.8672 18.5059" xmlns="http://www.w3.org/2000/svg"><g fill-opacity=".85"><path d="m17.3633 1.15234c-.9961-.99609-2.4024-1.15234-4.0723-1.15234h-8.10545c-1.64063 0-3.04688.15625-4.04297 1.15234-.996096.9961-1.14258 2.38282-1.14258 4.02344v8.11522c0 1.6699.146484 3.0567 1.14258 4.0528.99609.996 2.40234 1.1523 4.0625 1.1523h8.08592c1.6699 0 3.0762-.1563 4.0723-1.1523.9961-.9961 1.1426-2.3829 1.1426-4.0528v-8.08592c0-1.66992-.1465-3.06641-1.1426-4.05274zm-.4297 3.7793v8.62306c0 1.0058-.127 2.0508-.7227 2.6465-.5859.5859-1.6406.7226-2.6464.7226h-8.62309c-1.00586 0-2.06055-.1367-2.65625-.7226-.58594-.5957-.71289-1.6407-.71289-2.6465v-8.59376c0-1.02539.12695-2.08008.71289-2.66602.5957-.5957 1.66015-.72265 2.68554-.72265h8.5938c1.0058 0 2.0605.13671 2.6464.72265.5957.5957.7227 1.63086.7227 2.63672z"/><path d="m3.94531 14.9121h10.60549c.3418 0 .6152-.2637.6152-.6055 0-.332-.2734-.6054-.6152-.6054h-10.60549c-.33203 0-.60547.2734-.60547.6054 0 .3418.27344.6055.60547.6055zm.82031-7.36327h2.11915c.83984 0 1.42578-.57617 1.42578-1.42578v-1.49414c0-.84961-.58594-1.43555-1.42578-1.43555h-2.11915c-.8496 0-1.43554.58594-1.43554 1.43555v1.49414c0 .84961.58594 1.42578 1.43554 1.42578z"/></g></svg>
            <h4>Home screen widgets</h4>
            <p>Let users view live and personalized data (KPIs, stats, or schedule) on their home screen.</p>
        </div>
        <div>
            <svg viewBox="0 0 21.2598 20.8887" xmlns="http://www.w3.org/2000/svg"><path d="m9.46289 20.8789h1.97261c.752 0 1.3379-.459 1.504-1.1816l.4199-1.8262.3125-.1074 1.5918.9765c.6347.3907 1.3672.3028 1.9043-.2343l1.3672-1.3575c.5371-.5371.625-1.2793.2343-1.9043l-.9961-1.582.1172-.293 1.8262-.4296c.7129-.1661 1.1816-.7618 1.1816-1.504v-1.93355c0-.74218-.4589-1.32812-1.1816-1.5039l-1.8066-.43946-.127-.3125.9961-1.58203c.3906-.625.3125-1.35742-.2344-1.90429l-1.3672-1.36719c-.5273-.52735-1.2597-.625-1.8945-.23438l-1.5918.97657-.332-.12696-.4199-1.82617c-.1661-.722656-.752-1.18164-1.504-1.18164h-1.97261c-.75195 0-1.33789.458984-1.50391 1.18164l-.42968 1.82617-.33203.12696-1.58204-.97657c-.63476-.39062-1.37695-.29297-1.90429.23438l-1.35742 1.36719c-.54688.54687-.63477 1.27929-.23438 1.90429l.98633 1.58203-.11719.3125-1.80664.43946c-.722656.17578-1.18164.76172-1.18164 1.5039v1.93355c0 .7422.46875 1.3379 1.18164 1.504l1.82617.4296.10742.293-.98632 1.582c-.40039.625-.30274 1.3672.23437 1.9043l1.35742 1.3575c.53711.5371 1.2793.625 1.91407.2343l1.58203-.9765.3125.1074.42968 1.8262c.16602.7226.75196 1.1816 1.50391 1.1816zm.15625-1.5234c-.16602 0-.25391-.0684-.2832-.2246l-.58594-2.4219c-.5957-.1465-1.15234-.3809-1.57227-.6445l-2.1289 1.3085c-.11719.0879-.25391.0782-.36133-.0488l-1.15234-1.1523c-.10743-.1074-.11719-.2246-.03907-.3614l1.3086-2.1093c-.22461-.4102-.47852-.9668-.63477-1.5625l-2.42187-.5762c-.15625-.0293-.22461-.1172-.22461-.2832v-1.63086c0-.17578.05859-.25391.22461-.28321l2.41211-.58593c.15625-.63477.44922-1.21094.625-1.57227l-1.29883-2.10937c-.08789-.14649-.07813-.26368.02929-.38086l1.16211-1.13282c.11719-.11718.22461-.12695.3711-.04882l2.10937 1.27929c.41992-.23437 1.01563-.47851 1.60157-.64453l.57617-2.42187c.02929-.15625.11718-.22461.2832-.22461h1.66016c.166 0 .2539.06836.2734.22461l.5957 2.4414c.6055.15625 1.1328.40039 1.5723.63477l2.1191-1.28906c.1563-.07813.254-.06836.3809.04882l1.1523 1.13282c.1172.11718.1172.23437.0293.38086l-1.2988 2.10937c.1856.36133.4688.9375.625 1.57227l2.4219.58593c.1562.0293.2246.10743.2246.28321v1.63086c0 .166-.0781.2539-.2246.2832l-2.4316.5762c-.1563.5957-.4004 1.1523-.6348 1.5625l1.3086 2.1093c.0781.1368.0781.254-.0391.3614l-1.1426 1.1523c-.1171.127-.2441.1367-.3711.0488l-2.1289-1.3085c-.4199.2636-.9668.498-1.5625.6445l-.5957 2.4219c-.0195.1562-.1074.2246-.2734.2246zm.83006-5.1953c2.0606 0 3.7207-1.6602 3.7207-3.7207 0-2.06059-1.6601-3.72075-3.7207-3.72075-2.06053 0-3.72068 1.66016-3.72068 3.72075 0 2.0605 1.66015 3.7207 3.72068 3.7207zm0-1.5137c-1.22068 0-2.20701-.9863-2.20701-2.207 0-1.22075.98633-2.20708 2.20701-2.20708 1.2207 0 2.207.98633 2.207 2.20708 0 1.2207-.9863 2.207-2.207 2.207z" fill-opacity=".85"/></svg>
            <h4>iOS and Android support</h4>
            <p>Works seamlessly across all devices and platforms.</p>
        </div>
        <div>
            <svg viewBox="0 0 21.8524 20.1726" xmlns="http://www.w3.org/2000/svg"><path d="m.301179 17.8255c1.816411 2.4512 5.625001 3.1153 7.988281 1.3575 2.02144-1.4942 2.45114-3.8477.95703-5.8203-1.39648-1.875-3.4668-2.334-5.08789-1.1133-1.52344 1.1426-.98633 2.7832-1.83594 3.4179-.74218.5567-1.435543.2442-1.972653.6543-.3906246.3125-.517578.8789-.048828 1.5039zm1.992191-.1562c-.12696-.1367-.11719-.2734-.0293-.3613.13672-.127.68359-.166 1.10352-.586.94726-.9375.625-2.3144 1.71875-3.164.9082-.7227 2.16796-.459 3.01757.6347.95703 1.2403.625 2.7539-.69336 3.7696-1.47461 1.1621-3.75 1.1523-5.11718-.293zm7.55859-2.168c1.05464-.1172 1.94334-.6445 2.93944-1.6406 3.1348-3.125 7.9199-10.12695 8.3399-10.71289 1.2304-1.70898-.8789-3.798827-2.5977-2.597655-.5762.410156-7.5879 5.185545-10.71289 8.339845-.97656.98633-1.51367 1.8652-1.64062 2.9004l1.5332.4199c.01953-.7129.40039-1.4062 1.21094-2.207 3.09567-3.05666 9.94137-7.75393 10.36137-8.06643.332-.24414.7031.10743.4492.46875-.2539.3711-5.0098 7.30469-8.0567 10.35158-.8007.8008-1.416 1.123-2.08981 1.2012zm1.72854-2.4511 1.25-.3223c-.1367-1.8652-1.9629-3.72071-3.79885-3.87696l-.41992 1.24996c1.25-.0293 2.96877 1.6895 2.96877 2.9493z" fill-opacity=".85"/></svg>
            <h4>Fully branded experience</h4>
            <p>Maintain your product identity.</p>
        </div>
        <div>
            <svg viewBox="0 0 18.7305 25.3809" xmlns="http://www.w3.org/2000/svg"><path d="m7.38281 4.26758c1.16211 0 2.12891-.9668 2.12891-2.12891 0-1.171873-.9668-2.13867-2.12891-2.13867-1.17187 0-2.13867.966797-2.13867 2.13867 0 1.16211.9668 2.12891 2.13867 2.12891zm1.25977 16.02542c.38086.0976 1.02539-.0391 1.21094-.6641l1.41598-4.6973c.1465-.5078-.0781-1.0254-.6152-1.2109l-3.83789-1.3281.47851-3.27151c.01953-.19531.27344-.24414.36133-.05859l.82031 1.5918c.18555.3613.50782.5371.87891.5371h3.92573c.5372 0 .9864-.4199.9864-.9766 0-.5273-.4492-.97652-.9864-.97652h-3.32026l-1.2793-2.5293c-.41016-.82031-1.14258-1.39648-2.06055-1.57226l-.54687-.10742c-1.00586-.19532-1.81641.01953-2.44141.41015l-3.173826 1.97266c-.273437.18555-.458984.49805-.458984.84961v3.91598c0 .5371.449219.9961.986328.9961.537112 0 .986332-.459.986332-.9961v-3.38864l1.36718-.84961c.12696-.06836.27344.00977.24414.15625l-.44921 3.6231c-.17579 1.3867.2832 2.3339 1.875 2.705l4.07226.9571-1.10351 3.7011c-.14649.5078.04882 1.0352.66406 1.211zm-7.20703 3.5351c.35156.2344.98633.2344 1.34765-.2929l2.89063-4.3067c.13672-.2051.20508-.3613.24414-.6055l.41015-2.8808-1.5332-.3613c-.23437-.0489-.43945-.127-.63476-.1954l-.20508 3.0957-2.79297 4.1797c-.302735.4493-.195313 1.0645.27344 1.3672zm3.88672 1.5332h3.14453c.30273 0 .55664-.2539.55664-.5566v-1.8848h2.59766c.293 0 .5371-.2344.5371-.5469v-1.8945h2.6172c.3027 0 .5371-.2344.5371-.5371v-1.8945h2.5c.3027 0 .5566-.2442.5566-.5469 0-.293-.2539-.5469-.5566-.5469h-3.0371c-.3027 0-.5371.2539-.5371.5469v1.8945h-2.6172c-.3125 0-.5371.2539-.5371.5469v1.8945h-2.6172c-.29297 0-.52735.2344-.52735.5371v1.9043h-2.61718c-.3125 0-.53711.2344-.53711.5274 0 .3027.22461.5566.53711.5566z" fill-opacity=".85"/></svg>
            <h4>Frictionless user onboarding</h4>
            <p>Appio takes care of the end-to-end flow. No accounts, no passwords, no confusion.</p>
        </div>
        <div>
            <svg viewBox="0 0 19.8828 17.998" xmlns="http://www.w3.org/2000/svg"><path d="m4.4043 17.998h10.7129c1.9238 0 2.9492-.996 2.9492-2.9101v-10.4297h-1.5723v10.5078c0 .8789-.4882 1.3574-1.3574 1.3574h-10.75193c-.87891 0-1.35743-.4785-1.35743-1.3574v-10.5078h-1.57226v10.4297c0 1.9238 1.02539 2.9101 2.94922 2.9101zm2.13867-8.67183h6.44533c.4101 0 .7031-.2832.7031-.71289v-.3125c0-.42969-.293-.70312-.7031-.70312h-6.44533c-.41016 0-.69336.27343-.69336.70312v.3125c0 .42969.2832.71289.69336.71289zm-4.6875-3.93555h15.81053c1.2012 0 1.8555-.75195 1.8555-1.94335v-1.48438c0-1.191406-.6543-1.9433588-1.8555-1.9433588h-15.81053c-1.142579 0-1.85547.7519528-1.85547 1.9433588v1.48438c0 1.1914.654297 1.94335 1.85547 1.94335zm.40039-1.4746c-.48828 0-.68359-.20508-.68359-.69336v-1.03516c0-.48828.19531-.69336.68359-.69336h15.01954c.4883 0 .6738.20508.6738.69336v1.03516c0 .48828-.1855.69336-.6738.69336z" fill-opacity=".85"/></svg>
            <h4>Notification history</h4>
            <p>No more disappearing messages. Users can revisit missed notifications anytime.</p>
        </div>
        <div>
            <svg viewBox="0 0 24.2871 18.916" xmlns="http://www.w3.org/2000/svg"><path d="m.380859 15.9375c0 1.9336 1.025391 2.9395 2.988281 2.9395h12.00196c1.9531 0 2.9785-1.0059 2.9785-2.9395v-3.4473c0-.1562.1074-.2343.293-.1074.6152.4395 1.3281.752 2.1484.752 1.8457 0 3.4961-1.4844 3.4961-3.66214 0-2.16797-1.6504-3.65235-3.4961-3.65235-.8203 0-1.5332.3125-2.1484.75196-.1856.12695-.293.04882-.293-.10743v-3.52539c0-1.93359-1.0254-2.93945-2.9785-2.93945h-12.00196c-1.96289 0-2.988281 1.00586-2.988281 2.93945v4.31641c0 .82031.53711 1.30859 1.210941 1.30859.37109 0 .75195-.15625 1.13281-.50781.47851-.42969 1.01562-.69336 1.5918-.69336 1.11328 0 2.05078.83008 2.05078 2.08984 0 1.25978-.9375 2.08988-2.05078 2.08988-.57618 0-1.11329-.2735-1.5918-.7032-.38086-.3418-.76172-.498-1.13281-.498-.673831 0-1.210941.4883-1.210941 1.2988zm1.572261-.0781v-3.252c0-.459.23438-.2832.41993-.166.5957.3809 1.25.6641 2.02148.6641 1.83594 0 3.49609-1.4746 3.49609-3.65238 0-2.17773-1.66015-3.65234-3.49609-3.65234-.77148 0-1.42578.2832-2.02148.66406-.18555.11719-.41993.29297-.41993-.16601v-3.28125c0-.92774.53711-1.44531 1.44532-1.44531h11.94336c.9082 0 1.4355.51757 1.4355 1.44531v4.23828c0 .82031.5372 1.33789 1.211 1.33789.3711 0 .7519-.15625 1.1328-.50781.4785-.42969 1.0156-.70313 1.5918-.70313 1.1133 0 2.0508.83008 2.0508 2.08985 0 1.26954-.9375 2.09964-2.0508 2.09964-.5762 0-1.1133-.2735-1.5918-.7032-.3809-.3515-.7617-.5078-1.1328-.5078-.6738 0-1.211.5176-1.211 1.3379v4.1602c0 .9375-.5273 1.4453-1.4355 1.4453h-11.94336c-.90821 0-1.44532-.5078-1.44532-1.4453z" fill-opacity=".85"/></svg>
            <h4>API-first and no-code friendly</h4>
            <p>Use our API or use no-code tools.</p>
        </div>
        <div>
            <svg viewBox="0 0 20.2344 19.7949" xmlns="http://www.w3.org/2000/svg"><g fill-opacity=".85"><path d="m9.94141 1.67969c.45899 0 .83979-.38086.83979-.839846 0-.458985-.3808-.839844-.83979-.839844s-.83985.380859-.83985.839844c0 .458986.38086.839846.83985.839846zm2.79299.45898c.459 0 .8398-.38086.8398-.83984 0-.458986-.3808-.839846-.8398-.839846s-.8399.38086-.8399.839846c0 .45898.3809.83984.8399.83984zm2.539 1.2793c.459 0 .8399-.38086.8399-.83985 0-.45898-.3809-.83984-.8399-.83984-.4589 0-.8398.38086-.8398.83984 0 .45899.3809.83985.8398.83985zm2.002 2.01172c.459 0 .8398-.38086.8398-.83985 0-.45898-.3808-.83984-.8398-.83984s-.8399.38086-.8399.83984c0 .45899.3809.83985.8399.83985zm1.2793 2.5c.459 0 .8398-.38086.8398-.83985 0-.45898-.3808-.83984-.8398-.83984s-.8399.38086-.8399.83984c0 .45899.3809.83985.8399.83985zm.4785 2.80271c.459 0 .8398-.3808.8398-.83982 0-.45899-.3808-.83985-.8398-.83985s-.8398.38086-.8398.83985c0 .45902.3808.83982.8398.83982zm-.4785 2.8028c.459 0 .8398-.3809.8398-.8399s-.3808-.8398-.8398-.8398-.8399.3808-.8399.8398.3809.8399.8399.8399zm-1.2793 2.5097c.459 0 .8398-.3808.8398-.8398s-.3808-.8399-.8398-.8399-.8399.3809-.8399.8399.3809.8398.8399.8398zm-2.002 2.002c.459 0 .8399-.3809.8399-.8399s-.3809-.8398-.8399-.8398c-.4589 0-.8398.3808-.8398.8398s.3809.8399.8398.8399zm-2.539 1.2793c.459 0 .8398-.3809.8398-.8399s-.3808-.8398-.8398-.8398-.8399.3808-.8399.8398.3809.8399.8399.8399zm-2.79299.459c.45899 0 .83979-.3809.83979-.8399s-.3808-.8398-.83979-.8398-.83985.3808-.83985.8398.38086.8399.83985.8399zm-2.79297-.459c.45898 0 .83984-.3809.83984-.8399s-.38086-.8398-.83984-.8398c-.46875 0-.83985.3808-.83985.8398s.3711.8399.83985.8399zm-2.53906-1.2793c.45898 0 .83984-.3809.83984-.8399s-.38086-.8398-.83984-.8398c-.46876 0-.83985.3808-.83985.8398s.37109.8399.83985.8399zm-2.01172-2.002c.45898 0 .83984-.3808.83984-.8398s-.38086-.8399-.83984-.8399c-.45899 0-.83985.3809-.83985.8399s.38086.8398.83985.8398zm-1.2793-2.5097c.45898 0 .83984-.3809.83984-.8399s-.38086-.8398-.83984-.8398c-.458985 0-.839844.3808-.839844.8398s.380859.8399.839844.8399zm-.478516-2.8028c.458986 0 .839846-.3808.839846-.83982 0-.45899-.38086-.83985-.839846-.83985-.458985 0-.839844.38086-.839844.83985 0 .45902.380859.83982.839844.83982zm.478516-2.80271c.45898 0 .83984-.38086.83984-.83985 0-.45898-.38086-.83984-.83984-.83984-.458985 0-.839844.38086-.839844.83984 0 .45899.380859.83985.839844.83985zm1.2793-2.5c.45898 0 .83984-.38086.83984-.83985 0-.45898-.38086-.83984-.83984-.83984-.45899 0-.83985.38086-.83985.83984 0 .45899.38086.83985.83985.83985zm2.01172-2.01172c.45898 0 .83984-.38086.83984-.83985 0-.45898-.38086-.83984-.83984-.83984-.46876 0-.83985.38086-.83985.83984 0 .45899.37109.83985.83985.83985zm2.53906-1.2793c.45898 0 .83984-.38086.83984-.83984 0-.458986-.38086-.839846-.83984-.839846-.46875 0-.83985.38086-.83985.839846 0 .45898.3711.83984.83985.83984z"/><path d="m6.40625 13.7402c0 .7325.32227 1.0743 1.00586 1.0743h5.05859c.6738 0 .9961-.3418.9961-1.0743v-3.88668c0-.6836-.2832-1.01563-.8594-1.05469v-1.16211c0-1.79688-1.0742-2.99805-2.66599-2.99805-1.5918 0-2.66602 1.20117-2.66602 2.99805v1.16211c-.58594.03906-.86914.37109-.86914 1.05469zm1.88477-4.95114v-1.25976c0-1.14258.65429-1.9043 1.65039-1.9043.98629 0 1.65039.76172 1.65039 1.9043v1.25976z"/></g></svg>
            <h4>Private by design</h4>
            <p>Built with privacy in mind. Appio never stores personal data.</p>
        </div>
    </div>
</div>
</section>
<div class="id_093bc9a8__fade-in">
    <style>
#case-studies {
    /* extra scroll so on ios the pricing menu is not visible */
    scroll-margin-top: calc(var(--scroll-margin-top) + 1rem);
    max-width: var(--content-width);
    margin: auto;
    padding: 0 var(--content-padding);
}
#id_16b01d98 {
    margin: var(--section-gap) auto 0;
}
#id_16b01d98 h3 {
    font-size: 4em;
    text-align: center;
}
#id_16b01d98 div > p {
    text-align: center;
}
#id_16b01d98 div > p span {
    font-weight: 600;
    padding: 0 .15em;
    /*text-transform: capitalize;*/
}
#id_16b01d98 div > p span.id_16b01d98__real {
    color: rgb(0, 61, 90);
    background: rgb(201, 240, 255);
}
#id_16b01d98 div > p span.id_16b01d98__illustrative {
    color: rgb(86, 1, 89);
    background: rgb(249, 226, 251);
}
#id_16b01d98 main {
    margin-top: 2em;
    overflow-x: auto;

    -ms-overflow-style: none;
    scrollbar-width: none;
    scroll-snap-type: x mandatory;
}
#id_16b01d98 main div {
    display: flex;
    gap: 4rem;
    padding: .5rem 36rem;
}
#id_16b01d98 main a {
    position: relative;
    display: block;
    min-width: 28rem;
    box-shadow: var(--box-shadow);
    background: rgb(var(--box-background));
    border-radius: 1.5em;
    padding: 1.5em 2em 3em;
    overflow: hidden;
    text-decoration: none;
    scroll-snap-align: center;
}
#id_16b01d98 main a.id_16b01d98__illustrative::after {
    content: "illustrative";
    position: absolute;
    top: 1em;
    right: -2em;
    transform: rotate(45deg);
    color: rgb(86, 1, 89);
    background: rgb(249, 226, 251);
    padding: .2em 2em;
    font-weight: 500;
}
#id_16b01d98 main a h4 {
    font-size: 2em;
}
#id_16b01d98 main a:hover h4 {
    color: rgb(var(--btn-color));
}
#id_16b01d98 main a span {
    font-weight: 700;
    position: absolute;
    bottom: 1em;
    left: 2em;
}
#id_16b01d98 main a:hover span {
    color: rgb(var(--btn-color));
}
#id_16b01d98 main a span::after {
    content: "→";
    display: inline-block;
    margin-left: .25em;
    transition: transform 0.3s ease, opacity 0.3s ease;
    animation: none;
    font-weight: 500;
}
#id_16b01d98 main a:hover span::after {
    animation: arrowFly 0.6s ease forwards;
}
#id_16b01d98 main img {
    width: 50%;
    padding-bottom: .5em;
}
#id_16b01d98 aside {
    margin-top: 1em;
    display: flex;
    gap: 1em;
    justify-content: center;
}
#id_16b01d98 aside svg {
    display: block;
    cursor: pointer;
    user-select: none;
    background-color: rgb(232, 232, 237);
    border-radius: 50%;
    fill: rgba(0, 0, 0, 0.56);
}
#id_16b01d98 aside svg:hover,
#id_16b01d98 aside svg:focus
{
    background-color: rgb(236, 236, 239);
    fill: rgba(0, 0, 0, 0.64);
}
#id_16b01d98 aside svg:active {
    background-color: rgb(223, 223, 228);
}
@media (max-width: 790px) {
    #id_16b01d98 h3 {
        font-size: 3em;
    }
}
@media (max-width: 590px) {
    #case-studies {
        margin: 0 1em;
    }
    #id_16b01d98 h3 {
        font-size: 2em;
        text-align: left;
    }
    #id_16b01d98 div > p {
        text-align: left;
    }
}
@media (max-width: 550px) {
    #id_16b01d98 main a {
        min-width: 80vw;
    }
}
</style>
<div id="id_16b01d98">
    <div id="case-studies">
        <h3>Case studies</h3>
        <p>
            Some of these examples are <span class="id_16b01d98__illustrative">illustrative</span>,
            inspired by <span class="id_16b01d98__real">real product needs</span>.
            They show how Appio can add value today.
        </p>
    </div>

    <main id="id_16b01d98__slider">
        <div>
            <a href="/case-studies/fathom-analytics/" class="id_16b01d98__illustrative">
                
                    <img src="/assets/logos/fathom-analytics.svg" alt="Fathom Analytics Case Study">
                
                <p>Push notifications for traffic milestones and trend alerts.</p>
                <span>Read more</span>
            </a>
            <a href="/case-studies/cal-com/" class="id_16b01d98__illustrative">
                
                    <img src="/assets/logos/cal.svg" alt="Cal.com Case Study">
                
                <p>Meeting reminders via home screen widget.</p>
                <span>Read more</span>
            </a>
            <a href="/case-studies/kit/" class="id_16b01d98__illustrative">
                
                    <img src="/assets/logos/kit.svg" alt="Kit Case Study">
                
                <p>Marketing campaign updates and waitlist notifications.</p>
                <span>Read more</span>
            </a>
            <a href="/case-studies/nappy-counter/" class="id_16b01d98__illustrative">
                
                    <h4>Nappy Counter</h4>
                
                <p>One-tap tracking for diaper changes, visible at a glance.</p>
                <span>Read more</span>
            </a>
            <a href="/case-studies/days-alive/" class="id_16b01d98__illustrative">
                
                    <h4>Days Alive</h4>
                
                <p>Minimalist widget showing days since signup, birthday, or milestone.</p>
                <span>Read more</span>
            </a>
            <a href="/case-studies/appio/" >
                
                    <img src="/assets/logos/appio.svg" alt="Appio.so Case Study">
                
                <p>Real-time business metrics and instant notifications for growth tracking.</p>
                <span>Read more</span>
            </a>
        </div>
    </main>
    <aside>
        <svg id="id_16b01d98__left" width="36" height="36" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg"><path d="M20 25c-.384 0-.768-.146-1.06-.44l-5.5-5.5a1.5 1.5 0 0 1 0-2.12l5.5-5.5a1.5 1.5 0 1 1 2.12 2.12L16.622 18l4.44 4.44A1.5 1.5 0 0 1 20 25z"></path></svg>
        <svg id="id_16b01d98__right" width="36" height="36" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg" ><path d="M22.56 16.938l-5.508-5.5a1.493 1.493 0 0 0-2.116.003 1.502 1.502 0 0 0 .004 2.121L19.384 18l-4.444 4.438A1.502 1.502 0 0 0 15.996 25c.382 0 .764-.145 1.056-.438l5.508-5.5a1.502 1.502 0 0 0 0-2.125z"></path></svg>
    </aside>
</div>
<script>
    (function () {
        let
            index = 0,
            stepWidth = 0;

        const
            left = document.getElementById("id_16b01d98__left"),
            right = document.getElementById("id_16b01d98__right"),
            slider = document.getElementById("id_16b01d98__slider"),
            sliderStrip = slider.querySelector("div"),
            items = slider.querySelectorAll("a")

        function setup() {
            const
                sliderWidth = slider.getBoundingClientRect().width,
                gap = parseFloat(window.getComputedStyle(sliderStrip).gap),
                itemWidth = items[0] && items[0].getBoundingClientRect().width,
                padding = (sliderWidth - itemWidth) / 2;

            stepWidth = itemWidth + gap;
            items.forEach(item => item.style.width = `${itemWidth}px`);
            sliderStrip.style.paddingLeft = `${padding}px`
            sliderStrip.style.width = (itemWidth * items.length + gap * (items.length - 1) + 2 * padding) + "px";
            slide(1);
        }

        function slide(delta) {
            index = (index + delta + items.length) % items.length;
            slider.scrollTo({
                left: index * stepWidth,
                behavior: "smooth"
            })
        }

        // Start
        setup();

        window.addEventListener("resize", () => {
            setup();
            slide(1);
        });
        slider.addEventListener("scroll", () => {
            index  = Math.round(slider.scrollLeft / stepWidth);
        })
        left.addEventListener("click", (e) => {
            e.preventDefault();
            slide(-1)
        });
        right.addEventListener("click", (e) => {
            e.preventDefault();
            slide(1)
        });
    })()
</script>
</div>
<section class="id_093bc9a8__fade-in">
    <style>
#pricing {
    scroll-margin-top: var(--scroll-margin-top);
}
#id_b680e04f {
    margin: var(--section-gap) auto 0;
}
#id_b680e04f h2 {
    font-size: 5em;
    text-align: center;
}
#id_b680e04f main {
    margin: 2.5em auto 0;
    max-width: 41em;
    box-shadow: var(--box-shadow);
    border-radius: 1em;
    padding: 2em;
    background: rgb(var(--box-background));
}
#id_b680e04f h3 {
    text-align: center;
}
#id_b680e04f main a {
    margin: 1.5em auto;
    display: table;
    text-decoration: none;
    padding: .8em 1.5em;
    border-radius: 1.5em;
    font-weight: 700;
    font-size: 1.4em;
    user-select: none;
    background: rgb(var(--btn-color));
    color: #fff;
    box-shadow: rgba(0, 0, 0, 0.1) 0 1px 1px 0, rgba(0, 0, 0, 0.08) 0 2px 3px 0, rgba(0, 0, 0, 0.12) 0 4px 8px 0, rgba(6, 54, 109, 0.4) 0 -3px 2px 0 inset, rgba(255, 255, 255, 0.14) 0 2px .4px 0 inset;
}
#id_b680e04f main a:hover {
    background: rgb(var(--btn-hover-color));
}
#id_b680e04f main a:focus {
    box-shadow: none;
}
#id_b680e04f strong {
    display: block;
    margin-top: .3em;
}
@media (max-width: 790px) {
    #id_b680e04f h2 {
        font-size: 4em;
    }
    #id_b680e04f main {
        margin-top: 2em;
    }
}
@media (max-width: 490px) {
    #id_b680e04f h2 {
        font-size: 3em;
    }
    #id_b680e04f main {
        margin-top: 1.5em;
    }
    #id_b680e04f main a {
        margin: 1em auto;
    }
    .id_b680e04f__note {
        font-size: .8em;
    }
}
</style>
<div id="id_b680e04f">
    <div id="pricing">
        <h2>Pricing</h2>
    </div>
    <main>
        <h3>Free while we are in early access</h3>
        <p>
            Help us shape the future. Get direct access to the founder and priority support.
        </p>


        <a href="/list/">Start for free</a>

        <p class="id_b680e04f__note">
            We are looking for early partners who are enthusiastic about the product, open to giving feedback, and ready to experiment.
            If you are excited about what we are building, we want to hear from you.
        </p>
        <strong>Let's grow together!</strong>
    </main>
</div>
</section>
<section class="id_093bc9a8__fade-in">
    <style>
#id_d6de207f {
    margin: var(--section-gap) auto 0;
}
#id_d6de207f h3 {
    text-align: center;
    font-size: 3em;
}
#id_d6de207f main {
    margin: 2.5em auto 0;
    display: flex;
    flex-direction: column;
    border-radius: 1.5em;
    background: rgb(var(--box-background));
    border-color: #fafafa;
    box-shadow: var(--box-shadow);
    padding: .5em 1.5em;
    max-width: 60em;
}
#id_d6de207f main > div {
    cursor: pointer;
    border-bottom: solid 1px rgb(var(--border-color));
    padding: 1em 0;
    font-size: 1.1em;
}
#id_d6de207f main > div:last-child {
    border-bottom: none;
}
#id_d6de207f main > div > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
#id_d6de207f main h4 {
    font-weight: 600;
    font-size: 1.1em;
}
#id_d6de207f main p {
    font-size: .9em;
    margin: 0;
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    transition: max-height 0.2s ease, opacity 0.2s ease, padding 0.2s ease;
}
#id_d6de207f main .open p {
    max-height: 200em;
    opacity: 1;
    margin-top: .4em;
}
#id_d6de207f main svg {
    transition: transform 0.2s ease;
}
#id_d6de207f main .open svg {
    transform: rotate(180deg);
}
@media (max-width: 1150px) {
    #id_d6de207f h3 {
        font-size: 2.5em;
    }
}
@media (max-width: 590px) {
    #id_d6de207f main {
        margin-top: 1.5em;
    }
    #id_d6de207f h3 {
        font-size: 2em;
        text-align: left;
        padding: 0 .5em;
    }
}
</style>
<div id="id_d6de207f">
    <h3>Frequently Asked Questions</h3>

    <main>
            <div>
                <div>
                    <h4>Is Appio an app builder?</h4>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7797 6.22007C11.9201 6.3607 11.999 6.55132 11.999 6.75007C11.999 6.94882 11.9201 7.13945 11.7797 7.28007L8.52968 10.5301C8.38906 10.6705 8.19843 10.7494 7.99968 10.7494C7.80093 10.7494 7.61031 10.6705 7.46968 10.5301L4.21968 7.28007C4.0872 7.1379 4.01508 6.94985 4.01851 6.75555C4.02194 6.56125 4.10065 6.37586 4.23806 6.23845C4.37547 6.10104 4.56086 6.02233 4.75516 6.0189C4.94946 6.01547 5.13751 6.08759 5.27968 6.22007L7.99968 8.94007L10.7197 6.22007C10.8603 6.07962 11.0509 6.00073 11.2497 6.00073C11.4484 6.00073 11.6391 6.07962 11.7797 6.22007Z" fill="#000"></path></svg>
                </div>
                <p>No. Appio doesn’t build mobile apps. It gives your web app real mobile features like push notifications and widgets, without needing native development or dealing with app stores.</p>
            </div>
            <div>
                <div>
                    <h4>How does Appio save me money?</h4>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7797 6.22007C11.9201 6.3607 11.999 6.55132 11.999 6.75007C11.999 6.94882 11.9201 7.13945 11.7797 7.28007L8.52968 10.5301C8.38906 10.6705 8.19843 10.7494 7.99968 10.7494C7.80093 10.7494 7.61031 10.6705 7.46968 10.5301L4.21968 7.28007C4.0872 7.1379 4.01508 6.94985 4.01851 6.75555C4.02194 6.56125 4.10065 6.37586 4.23806 6.23845C4.37547 6.10104 4.56086 6.02233 4.75516 6.0189C4.94946 6.01547 5.13751 6.08759 5.27968 6.22007L7.99968 8.94007L10.7197 6.22007C10.8603 6.07962 11.0509 6.00073 11.2497 6.00073C11.4484 6.00073 11.6391 6.07962 11.7797 6.22007Z" fill="#000"></path></svg>
                </div>
                <p>No developers, no mobile design, no agencies, and no ongoing maintenance costs. Fast results at a fraction of the cost.</p>
            </div>
            <div>
                <div>
                    <h4>How can Appio make me money?</h4>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7797 6.22007C11.9201 6.3607 11.999 6.55132 11.999 6.75007C11.999 6.94882 11.9201 7.13945 11.7797 7.28007L8.52968 10.5301C8.38906 10.6705 8.19843 10.7494 7.99968 10.7494C7.80093 10.7494 7.61031 10.6705 7.46968 10.5301L4.21968 7.28007C4.0872 7.1379 4.01508 6.94985 4.01851 6.75555C4.02194 6.56125 4.10065 6.37586 4.23806 6.23845C4.37547 6.10104 4.56086 6.02233 4.75516 6.0189C4.94946 6.01547 5.13751 6.08759 5.27968 6.22007L7.99968 8.94007L10.7197 6.22007C10.8603 6.07962 11.0509 6.00073 11.2497 6.00073C11.4484 6.00073 11.6391 6.07962 11.7797 6.22007Z" fill="#000"></path></svg>
                </div>
                <p>Offer mobile features as part of your higher-tier plans, or use them to reduce churn and boost engagement. Get ahead of your competitors by offering better features.</p>
            </div>
            <div>
                <div>
                    <h4>How does Appio integrate into my current web app?</h4>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7797 6.22007C11.9201 6.3607 11.999 6.55132 11.999 6.75007C11.999 6.94882 11.9201 7.13945 11.7797 7.28007L8.52968 10.5301C8.38906 10.6705 8.19843 10.7494 7.99968 10.7494C7.80093 10.7494 7.61031 10.6705 7.46968 10.5301L4.21968 7.28007C4.0872 7.1379 4.01508 6.94985 4.01851 6.75555C4.02194 6.56125 4.10065 6.37586 4.23806 6.23845C4.37547 6.10104 4.56086 6.02233 4.75516 6.0189C4.94946 6.01547 5.13751 6.08759 5.27968 6.22007L7.99968 8.94007L10.7197 6.22007C10.8603 6.07962 11.0509 6.00073 11.2497 6.00073C11.4484 6.00073 11.6391 6.07962 11.7797 6.22007Z" fill="#000"></path></svg>
                </div>
                <p>Insert the short JavaScript snippet into your web app and automate actions via our <a href="https://docs.appio.so/" target="_blank">API</a>, or no-code tools like Zapier.</p>
            </div>
            <div>
                <div>
                    <h4>Can I customise Appio to match my brand?</h4>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7797 6.22007C11.9201 6.3607 11.999 6.55132 11.999 6.75007C11.999 6.94882 11.9201 7.13945 11.7797 7.28007L8.52968 10.5301C8.38906 10.6705 8.19843 10.7494 7.99968 10.7494C7.80093 10.7494 7.61031 10.6705 7.46968 10.5301L4.21968 7.28007C4.0872 7.1379 4.01508 6.94985 4.01851 6.75555C4.02194 6.56125 4.10065 6.37586 4.23806 6.23845C4.37547 6.10104 4.56086 6.02233 4.75516 6.0189C4.94946 6.01547 5.13751 6.08759 5.27968 6.22007L7.99968 8.94007L10.7197 6.22007C10.8603 6.07962 11.0509 6.00073 11.2497 6.00073C11.4484 6.00073 11.6391 6.07962 11.7797 6.22007Z" fill="#000"></path></svg>
                </div>
                <p>Yes. You control your brand across the entire user journey. Upload your logo, title, and description.</p>
            </div>
            <div>
                <div>
                    <h4>Is Appio secure and compliant?</h4>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7797 6.22007C11.9201 6.3607 11.999 6.55132 11.999 6.75007C11.999 6.94882 11.9201 7.13945 11.7797 7.28007L8.52968 10.5301C8.38906 10.6705 8.19843 10.7494 7.99968 10.7494C7.80093 10.7494 7.61031 10.6705 7.46968 10.5301L4.21968 7.28007C4.0872 7.1379 4.01508 6.94985 4.01851 6.75555C4.02194 6.56125 4.10065 6.37586 4.23806 6.23845C4.37547 6.10104 4.56086 6.02233 4.75516 6.0189C4.94946 6.01547 5.13751 6.08759 5.27968 6.22007L7.99968 8.94007L10.7197 6.22007C10.8603 6.07962 11.0509 6.00073 11.2497 6.00073C11.4484 6.00073 11.6391 6.07962 11.7797 6.22007Z" fill="#000"></path></svg>
                </div>
                <p>Yes. Appio does not store any personal data. Privacy is built in by design.</p>
            </div>
            <div>
                <div>
                    <h4>Why are push notifications better than email?</h4>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7797 6.22007C11.9201 6.3607 11.999 6.55132 11.999 6.75007C11.999 6.94882 11.9201 7.13945 11.7797 7.28007L8.52968 10.5301C8.38906 10.6705 8.19843 10.7494 7.99968 10.7494C7.80093 10.7494 7.61031 10.6705 7.46968 10.5301L4.21968 7.28007C4.0872 7.1379 4.01508 6.94985 4.01851 6.75555C4.02194 6.56125 4.10065 6.37586 4.23806 6.23845C4.37547 6.10104 4.56086 6.02233 4.75516 6.0189C4.94946 6.01547 5.13751 6.08759 5.27968 6.22007L7.99968 8.94007L10.7197 6.22007C10.8603 6.07962 11.0509 6.00073 11.2497 6.00073C11.4484 6.00073 11.6391 6.07962 11.7797 6.22007Z" fill="#000"></path></svg>
                </div>
                <p>Reliable delivery. No spam filters, no tracking pixels, no false-positive reporting. Just direct delivery with reliable open tracking.</p>
            </div>
            <div>
                <div>
                    <h4>Does Appio provide an SDK?</h4>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7797 6.22007C11.9201 6.3607 11.999 6.55132 11.999 6.75007C11.999 6.94882 11.9201 7.13945 11.7797 7.28007L8.52968 10.5301C8.38906 10.6705 8.19843 10.7494 7.99968 10.7494C7.80093 10.7494 7.61031 10.6705 7.46968 10.5301L4.21968 7.28007C4.0872 7.1379 4.01508 6.94985 4.01851 6.75555C4.02194 6.56125 4.10065 6.37586 4.23806 6.23845C4.37547 6.10104 4.56086 6.02233 4.75516 6.0189C4.94946 6.01547 5.13751 6.08759 5.27968 6.22007L7.99968 8.94007L10.7197 6.22007C10.8603 6.07962 11.0509 6.00073 11.2497 6.00073C11.4484 6.00073 11.6391 6.07962 11.7797 6.22007Z" fill="#000"></path></svg>
                </div>
                <p>No. Appio does not integrate into existing mobile apps, it completely removes the need for them. This is one of Appio’s biggest advantages. Appio is a subscription-based service (SaaS) that you control via our web app or <a href="https://docs.appio.so/" target="_blank">API</a>. There’s no SDK to install or manage.</p>
            </div>
            <div>
                <div>
                    <h4>Who created Appio?</h4>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7797 6.22007C11.9201 6.3607 11.999 6.55132 11.999 6.75007C11.999 6.94882 11.9201 7.13945 11.7797 7.28007L8.52968 10.5301C8.38906 10.6705 8.19843 10.7494 7.99968 10.7494C7.80093 10.7494 7.61031 10.6705 7.46968 10.5301L4.21968 7.28007C4.0872 7.1379 4.01508 6.94985 4.01851 6.75555C4.02194 6.56125 4.10065 6.37586 4.23806 6.23845C4.37547 6.10104 4.56086 6.02233 4.75516 6.0189C4.94946 6.01547 5.13751 6.08759 5.27968 6.22007L7.99968 8.94007L10.7197 6.22007C10.8603 6.07962 11.0509 6.00073 11.2497 6.00073C11.4484 6.00073 11.6391 6.07962 11.7797 6.22007Z" fill="#000"></path></svg>
                </div>
                <p>Appio is being built by a team led by serial entrepreneur <a href="https://www.michalgondar.com/" target="_blank">Michal Gondar</a>.</p>
            </div>
    </main>
</div>
<script>
    (function () {
        const items = document.querySelectorAll("#id_d6de207f main > div");
        items.forEach(i => {
            i.addEventListener("click", () => {
                if (i.classList.contains("open")) {
                    i.classList.remove("open");
                } else {
                    i.classList.add("open");
                }
            });
        });
    })();
</script>
</section>
<section class="id_093bc9a8__fade-in">
    <style>
#id_10975e55 {
    margin: var(--section-gap) auto 0;
    text-align: center;
}
#id_10975e55 h2 {
    font-size: 3em;
}
#id_10975e55 #cta-start {
    margin: 2em auto;
}
#id_10975e55 p span {
    color: rgb(128, 0, 0);
    background: rgb(255, 210, 225);
    padding: .1em .2em;
    border-radius: .2em;
    font-weight: 700;
}

#id_10975e55__cta {
    text-decoration: none;
    font-weight: 700;
}
#id_10975e55__cta::after {
    content: "→";
    display: inline-block;
    margin-left: .25em;
    transition: transform 0.3s ease, opacity 0.3s ease;
    animation: none;
    font-weight: 500;
}
#id_10975e55__cta:hover::after {
    animation: arrowFly 0.6s ease forwards;
}
@media (max-width: 490px) {
    #id_10975e55 h2 {
        font-size: 1.4em;
    }
}
</style>
<div id="id_10975e55">
    <h2>
        Give your users the mobile<br>
        experience they’ve been waiting for.
    </h2>

    <style>.frame {
    /* key dimensions */
    --dimension: 200px;

    /* Movement distances */
    --hover-travel: 3px;
    --press-travel: calc(var(--dimension) / 10);

    /* Color customization */
    --color: black;
    --brightness: 1;
    --blend-mode: color;

    /* Transition settings */
    --transition-duration: 0.4s;
    --transition-easing: linear(0, 0.008 1.1%, 0.031 2.2%, 0.129 4.8%, 0.257 7.2%, 0.671 14.2%, 0.789 16.5%, 0.881 18.6%, 0.957 20.7%, 1.019 22.9%, 1.063 25.1%, 1.094 27.4%, 1.114 30.7%, 1.112 34.5%, 1.018 49.9%, 0.99 59.1%, 1);

    /* Start animation settings */
    --fade-duration: .3s;
    --fade-delay: .3s;
    --start-scale: .8;

    /* Style */
    position: relative;
    width: var(--dimension);
    height: var(--dimension);
    cursor: pointer;
    container-type: inline-size;
    touch-action: manipulation;
    overflow: hidden;

    /* Start invisible and scaled down, then fade in and zoom in */
    opacity: 0;
    transform: scale(var(--start-scale));
    animation: frameStartAnimation var(--fade-duration) ease-out var(--fade-delay) forwards;
}

.frame:focus-visible {
    outline: none;
}

.frame,
.frame .frame__key,
.frame .frame__text
{
    -webkit-user-select: none; /* ios Safari */
    user-select: none;
}

.frame svg {
    /* Disable image dragging on MacOS Safari */
    -webkit-user-drag: none;
}

.frame .frame__base {
    width: 100%; height: 100%;
}

.frame .frame__key {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    filter: brightness(var(--brightness));
    -webkit-appearance: none;

    transition-property: all;
    transition-duration: var(--transition-duration);
    transition-timing-function: var(--transition-easing);
}

.frame .frame__cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%; height: 100%;
}

.frame:hover .frame__key,
.frame:hover .frame__text
{
    margin-top: var(--hover-travel);
}

.frame:active .frame__key,
.frame:active .frame__text,
    /* :active to support Android browsers, we use JS and __active */
.frame.__active .frame__key,
.frame.__active .frame__text
{
    margin-top: var(--press-travel);
}

.frame .frame__text {
    font-family: sans-serif;
    color: white;
    white-space: nowrap;

    position: absolute;
    width: 100%;
    height: 100%;
    top: 50%;
    left: 50%;
    text-align: center;
    align-content: center;
    translate: -50% -50%;
    transform: rotateY(0deg) rotateX(52.3deg) rotateZ(29deg) translateY(-28%) translateX(-16%);
    margin: 0;
    padding: 0;

    transition-property: all;
    transition-timing-function: var(--transition-easing);
    transition-duration: var(--transition-duration);
}

.frame .frame__text {
    font-size: calc(var(--dimension) / 10);
}
@container (min-width: 0px) {
    .frame .frame__text {
        font-size: 10cqw;
    }
}

/* Button fade-in and zoom-in animation */
@keyframes frameStartAnimation {
    from {
        opacity: 0;
        transform: scale(var(--start-scale));
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}</style>
    <div id="cta-start" class="frame" data-href="/list/">
        <img alt="image" src="/assets/button/base.svg" class="frame__base">
        <img alt="image" src="/assets/button/key.svg" class="frame__key">
        <img alt="image" src="/assets/button/cover.svg" class="frame__cover">
        <span class="frame__text">Start</span>
    </div>

    <script>(function () { "use strict";

const downSound = new Audio('/assets/button/key-down.mp3');
const upSound = new Audio('/assets/button/key-up.mp3');

downSound.preload = 'auto';
upSound.preload = 'auto';

const el = document.getElementById('cta-start');

// Track button press state
let isPressed = false;

function playSound(audio) {
    audio.currentTime = 0;
    const playPromise = audio.play();

    if (playPromise !== undefined) {
        playPromise.catch(error => {
            console.warn('Audio playback failed:', error);
        });
    }
}

function playDownSound() {
    if (!isPressed) {
        isPressed = true;
        /* __active to support Android browsers */
        el.classList.add('__active');
        playSound(downSound);
    }
}

function playUpSound() {
    if (isPressed) {
        isPressed = false;
        /* __active to support Android browsers */
        el.classList.remove('__active');
        playSound(upSound);
    }
}

el.addEventListener('mousedown', playDownSound);
document.addEventListener('mouseup', playUpSound);

let touchTarget = null;
el.addEventListener('touchstart', (e) => {
    // Disabled text selection on iOS Safari
    e.preventDefault();

    // Remember the target element to handle touchend
    touchTarget = e.target.parentElement;

    playDownSound()
}, { passive: false });
el.addEventListener('touchend', playUpSound);
el.addEventListener('touchcancel', playUpSound);

// Reset button state when browser/tab loses focus
window.addEventListener('blur', playUpSound);

// Handle visibility change (when tab becomes hidden)
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        playUpSound()
    }
});

// Handle click event
function handleClick() {
    const href = el.dataset.href
    if (href) {
        setTimeout(() => {
            window.location.href = href
        }, 300)
    }
}
function touchClick(e) {
    if (e.changedTouches) {
        const touch = e.changedTouches[0];
        const target = document.elementFromPoint(touch.clientX, touch.clientY).parentElement;
        if (target !== touchTarget) {
            touchTarget = null;
            return
        }
    }

    handleClick();
}
if ('ontouchend' in document.documentElement) {
    el.addEventListener('touchend', touchClick);
} else {
    el.addEventListener('click', handleClick);
}
 })();</script>
    <noscript>
        <a href="https://appio.so/list/">Start</a>
    </noscript>

    <p>
        <span>No</span> app stores. <span>No</span> mobile developers. <span>No</span> slowdowns.
        <br><br>
        Not convinced yet?<br>
        <a id="id_10975e55__cta" href="https://demo.appio.so/">See Appio in action</a>
    </p>
</div>
</section>

<script>
    (function () {
        document.querySelectorAll(".id_093bc9a8__fade-in").forEach(s => {
            window.viewPortObserver.observe(s);
        });
    })();
</script>
</div>
<style>
#id_94c40786__container {
    position: fixed;
    top: 0; bottom: 0;
    left: 0; right: 0;
    z-index: -1;
    color: #b0aea5;
    font-size: .8em;
    background: rgb(var(--outer-background-color));
    padding: 5em 0;
    letter-spacing: 0;
}
#id_94c40786 footer {
    position: fixed;
    bottom: 2em;
    left: 0; right: 0;
}
#id_94c40786 footer > div {
    max-width: var(--content-width);
    margin: 0 auto;
    padding: 0 var(--content-padding);
}
#id_94c40786 menu {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
#id_94c40786 menu:last-of-type {
    padding-bottom: 0.5em;
    border-bottom: solid 1px rgba(176, 174, 165, .2);
}
#id_94c40786 a,
#id_94c40786 span
{
    position: relative;
    display: block;
    padding: .5em 1em;
    text-decoration: none;
    color: #b0aea5;
    white-space: nowrap;
}
#id_94c40786 a:hover {
    text-decoration: underline;
}
#id_94c40786 span {
    opacity: .6;
}
#id_94c40786 span::after {
    content: "→";
    font-size: .8em;
    margin-left: .3em;
}
#id_94c40786 aside {
    padding-left: 1em;
    font-size: .85em;
    max-width: var(--content-width);
    margin: 1em auto;
}
#id_94c40786 a[target]::after {
    position: absolute;
    content: "";
    display: inline-block;
    top: 0.5em;
    right: 0;
    border: 0.25em solid transparent;
    border-bottom-color: currentColor;
    transform: rotate(45deg);
    opacity: .5;
}
#id_94c40786 a[target]:hover::after {
    opacity: 1;
}
</style>
<div id="id_94c40786">
    <div id="id_94c40786__container">
        <footer>
            <div>
                <menu>
                    <li>
                        <a href="/blog/">Blog</a>
                    </li>
                    <li>
                        <a href="/case-studies/">Case studies</a>
                    </li>
            
            
            
                    <li>
                        <a href="/contact/">Contact</a>
                    </li>
                    <li>
                        <a href="/legal">Legal</a>
                    </li>
                    <li>
                        <a href="https://docs.appio.so/" target="_blank">Docs</a>
                    </li>
                    <li>
                        <a href="https://status.appio.so/" target="_blank" rel="noopener noreferrer">Status</a>
                    </li>
                </menu>
                <menu>
                    <li>
                        <span>Integrations</span>
                    </li>
                    <li>
                        <a href="/integrations/zapier/">Zapier</a>
                    </li>
                </menu>
                <aside>
                    © 2025 Appio Limited. All rights reserved.
                </aside>
            </div>
        </footer>
    </div>
</div>




</body>
</html>
