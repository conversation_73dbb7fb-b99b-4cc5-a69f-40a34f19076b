
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Appio: Privacy Policy</title>

    <meta name="author" content="Appio.so">
    <meta name="description" content="A<PERSON><PERSON> adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">

    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Open graph -->
    <meta property="og:url" content="https://appio.so">
    <meta property="og:type" content="website">
    <meta property="og:title" content="Appio: Mobile Features for Your Web App">
    <meta property="og:description" content="Appio adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta property="og:image" content="/assets/images/social.png">

    <!-- Twitter -->
    <meta name="twitter:url" content="https://appio.so">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@appio_so">
    <meta name="twitter:creator" content="@appio_so">
    <meta name="twitter:title" content="Appio: Mobile Features for Your Web App">
    <meta name="twitter:description" content="Appio adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta name="twitter:image" content="/assets/images/social.png">
    <meta name="twitter:image:src" content="/assets/images/social.png">

    <style>@charset "UTF-8";

:root {
    --outer-background-color: 20,20,20;
    /*--background: 255,255,255;*/
    /*--box-background: 245,245,247;*/
    --background: 245,245,247;
    --box-background: 255,255,255;
    --text-color: 20,20,20;
    --border-color: 233,236,239;
    --content-width: 1200px;
    --content-padding: 1rem;
    --section-gap: 8rem;
    --scroll-margin-top: 7rem;
    --btn-color: 1,122,255;
    --btn-hover-color: 1,113,227;
    /*--btn-color: 1,113,227;*/
    /*--btn-hover-color: 0, 102, 204;*/
    --btn-shadow: inset 0 0 2px 0 #fcfcfc, 0 1px 1px 1px #d1d1d1;
    --box-shadow: rgba(13, 19, 27, 0.25) 0 0 1px 0, rgba(13, 19, 27, 0.05) 0 2px 1px 0;
    --font: "SF Pro Display", "SF Pro Icons", "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;

    /*--frame-border-width: 10;*/
    /*--frame-border-radius: 15;*/

    font-feature-settings: "liga" 1, "calt" 1; /* fix for Chrome */
}
@supports (font-variation-settings: normal) {
    :root {
        --font: "SF Pro Display", "SF Pro Icons", "InterVariable", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
    }
}
@media (max-width: 1300px) {
    :root {
        --content-width: auto;
        --content-padding: 3rem;
    }
}
@media (max-width: 790px) {
    :root {
        --content-padding: 1.5rem;
        --section-gap: 5rem;
        --scroll-margin-top: 5.4rem;
    }
}

section {
    max-width: var(--content-width);
    margin: auto;
    padding: 0 var(--content-padding);
}

html {
    font-family: var(--font);
    font-size: 106.25%;
    quotes: "“" "”";
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;

    /*background: rgb(var(--outer-background-color));*/
    /*scrollbar-color: rgb(255,255,255/.3) rgb(var(--outer-background-color));*/
}

html,
body
{
    /* prevent bouncy scroll on MacOS and iOS */
    overscroll-behavior: none;
}

/*html::before,*/
/*html::after {*/
/*    content: "";*/
/*    position: fixed;*/
/*    z-index: 100000;*/
/*    top: 0;*/
/*    bottom: 0;*/
/*    left: 0;*/
/*    right: 0;*/
/*    border: solid calc(var(--frame-border-width) * 1px) rgb(var(--outer-background-color));*/
/*    pointer-events: none;*/
/*}*/
/*html::after {*/
/*    z-index: 100001;*/
/*    border-radius: calc((var(--frame-border-width) + var(--frame-border-radius)) * 1px);*/
/*}*/

*, *::before, *::after {
    box-sizing: border-box;
}

html,body,ul,ol,li,dl,dt,dd,h1,h2,h3,h4,h5,h6,hgroup,p,blockquote,figure,form,fieldset,input,legend,pre,abbr,button {
    margin: 0;
    padding: 0;
}

dt {
    font-weight: 700;
    margin-top: 1em;
}

pre,code,address,caption,th,figcaption {
    font-size: 1em;
    font-weight: normal;
    font-style: normal;
}

code {
    font-family: "SF Mono", SFMono-Regular, ui-monospace, Menlo, monospace;
    font-weight: inherit;
    letter-spacing: 0;
    display: block;
    padding: 1em;
}

fieldset,iframe {
    border: 0;
}

caption,th {
    text-align: left;
}

main,summary,details {
    display: block;
}

audio,canvas,video,progress {
    vertical-align: baseline;
}

body {
    font-size: 1rem;
    line-height: 1.4705882353;
    font-weight: 400;
    letter-spacing: -0.022em;
    font-family: var(--font);
    background-color: rgb(var(--background));
    color: rgb(var(--text-color));
    font-style: normal;
    min-height: 100vh; /* to make background fill the whole screen */
}

body,button,input,textarea,select {
    font-synthesis: none;
    -moz-font-feature-settings: "kern";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

textarea {
    color: rgb(var(--text-color));
}

h1,h2,h3,h4,h5,h6 {
    font-weight: 700;
    color: rgb(var(--text-color));
    font-family: var(--font);
}

h1 img,h2 img,h3 img,h4 img,h5 img,h6 img {
    display: block;
    margin: 0;
}

h1+*,h2+*,h3+*,h4+*,h5+*,h6+* {
    margin-top: 0.8em;
}

h1+h1,h1+h2,h1+h3,h1+h4,h1+h5,h1+h6,h2+h1,h2+h2,h2+h3,h2+h4,h2+h5,h2+h6,h3+h1,h3+h2,h3+h3,h3+h4,h3+h5,h3+h6,h4+h1,h4+h2,h4+h3,h4+h4,h4+h5,h4+h6,h5+h1,h5+h2,h5+h3,h5+h4,h5+h5,h5+h6,h6+h1,h6+h2,h6+h3,h6+h4,h6+h5,h6+h6 {
    margin-top: 0.4em;
}

p+h1,ul+h1,ol+h1,p+h2,ul+h2,ol+h2,p+h3,ul+h3,ol+h3,p+h4,ul+h4,ol+h4,p+h5,ul+h5,ol+h5,p+h6,ul+h6,ol+h6 {
    margin-top: 1.6em;
}

p+*,ul+*,ol+*,dl+* {
    margin-top: 0.8em;
}

ul,ol {
    margin-inline-start:1.1764705882em;
}

ul ul,ul ol,ol ul,ol ol {
    margin-top: 0;
    margin-bottom: 0;
}

nav ul,nav ol {
    margin: 0;
    list-style: none;
}

li li {
    font-size: 1em;
}

b,strong {
    font-weight: 700;
}

em,i,cite,dfn {
    font-style: italic;
}

abbr {
    border: 0;
}

a {
    color: rgb(var(--text-color));
    letter-spacing: inherit;
}

/* a:link,a:visited,a:active,a:disabled {} */

a:active {
    text-decoration: none;
}

a.disabled,a :disabled {
    opacity: 0.42;
}

p+a {
    display: inline-block
}

sup,sub {
    position: relative;
    font-size: 0.6em;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

hr {
    border: none;
    height: 1px;
    background-color: rgb(var(--border-color));
    margin: 3em 0;
}

.nowrap {
    display: inline-block;
    text-decoration: inherit;
    white-space: nowrap;
}

.clear {
    clear: both;
}

h1 {
    font-size: 2.8235294118em;
    line-height: 1.1;
}

h2 {
    font-size: 1.8823529412em;
    line-height: 1.125;
    letter-spacing: .004em;
}

h3 {
    font-size: 1.6470588235em;
    line-height: 1.1428571429;
    letter-spacing: .007em;
}

h4 {
    font-size: 1.4117647059em;
    line-height: 1.1666666667;
    letter-spacing: .009em;
}

h5 {
    font-size: 1.2941176471em;
    line-height: 1.1818181818;
    letter-spacing: .01em;
}

h6 {
    font-size: 1em;
    line-height: 1.4705882353;
    letter-spacing: -.022em;
}

p {
    /* font-size: 1.1176470588em; */
    /* line-height: 1.4211026316; */
    font-weight: 400;
    letter-spacing: .012em;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
    min-width: 100%;
    font-size: .8235294118em;
    line-height: 1.4211026316;
    font-weight: 400;
    letter-spacing: -.016em;
    border-style: hidden;
    empty-cells: hide;
}

td, th {
    width: 1%;
    -webkit-hyphens: auto;
    hyphens: auto;
    min-width: 10em;
    border-style: solid;
    border-width: 1px 0;
    padding: .5882352941em;
}

th {
    border-color: rgb(var(--border-color));
    font-weight: 600;
    word-break: keep-all;
}

td {
    border-color: rgb(var(--border-color));
    word-break: break-word;
    color: var(--text-color);
}

/* Global animations */
/* clean-css ignore:start */
@keyframes fadeIn {
    from {
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
@keyframes arrowFly {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    50% {
        transform: translateX(0.5em);
        opacity: 0;
    }
    51% {
        transform: translateX(-0.5em);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}
/* clean-css ignore:end */</style>
    <style>/* Variable fonts usage:
:root { font-family: "Inter", sans-serif; }
@supports (font-variation-settings: normal) {
  :root { font-family: "InterVariable", sans-serif; font-optical-sizing: auto; }
} */
@font-face {
  font-family: InterVariable;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("/assets/fonts/inter-4-1/InterVariable.woff2") format("woff2");
}
@font-face {
  font-family: InterVariable;
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url("/assets/fonts/inter-4-1/InterVariable-Italic.woff2") format("woff2");
}

/* static fonts */
@font-face { font-family: "Inter"; font-style: normal; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Thin.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ThinItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraLight.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraLightItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Light.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-LightItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Regular.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Italic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Medium.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-MediumItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-SemiBold.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-SemiBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Bold.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-BoldItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraBold.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Black.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-BlackItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Thin.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ThinItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraLight.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraLightItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Light.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-LightItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Regular.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Italic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Medium.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-MediumItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-SemiBold.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-SemiBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Bold.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-BoldItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraBold.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Black.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-BlackItalic.woff2") format("woff2"); }

@font-feature-values InterVariable {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
@font-feature-values Inter {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
@font-feature-values InterDisplay {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
</style>

    <!-- Fonts -->
    <link rel="preload" href="/assets/fonts/inter-4-1/InterVariable.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <script>
        // Smooth scrolling only for on-page links. Instant scroll for external and back links
        window.addEventListener("load", () => {
            document.documentElement.style.scrollBehavior = "smooth";
        });

        // Adding class to elements in viewport
        window.viewPortObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add("js-in-viewport");
                    window.viewPortObserver.unobserve(entry.target);
                }
            });
        });
    </script>
</head>
<body id="top">

<style>
#id_f05f6623 {
    position: relative;
    padding-top: 6em;
    background: rgb(var(--background));
    margin-bottom: 14em; /* footer height + rounded corners bar. allow for 2 lines on mobile */
    padding-bottom: 3em;
    min-height: 100vh;
}
#id_f05f6623::before {
    content: "";
    background: rgb(var(--background));
    display: block;
    position: absolute;
    height: 3em;
    bottom: -1.5em;
    border-radius: calc(var(--frame-border-radius, 20) * 1px);;
    left: calc(var(--frame-border-width, 0) * 1px);
    right: calc(var(--frame-border-width, 0) * 1px);
}
@media (max-width: 790px) {
    #id_f05f6623 {
        padding-top: 4em;
    }
}
</style>
<style>
#id_a39c9407 {
    position: fixed;
    top: -1px;
    left: 0;
    right: 0;
    padding: 1em 0;
    z-index: 1000;
}
#id_a39c9407::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(var(--background), .8);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
    backdrop-filter: saturate(180%) blur(20px);
    z-index: 1001;
}
.id_a39c9407__scrolled {
    border-bottom: 1px solid rgba(233, 236, 239, .5);
}
#id_a39c9407 nav {
    position: sticky;
    z-index: 1002;
    width: var(--content-width);
    margin: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
@media (max-width: 1300px) {
    #id_a39c9407 nav {
        padding: 0 1em;
    }
}
#id_a39c9407__wrapper {
    display: flex;
    align-items: center;
    gap: 1em;
}
#id_a39c9407__logo {
    display: flex;
    align-items: center;
    gap: .35em;
    text-decoration: none;
    color: inherit;
    padding: .4em .5em .5em;
    font-weight: 700;
    font-size: 1.5em;
    user-select: none;
}
#id_a39c9407__logo * {
    transition: fill 0.3s ease, transform 0.3s ease;
}
#id_a39c9407__logo:hover svg {
    transform: scale(1.3);
}
#id_a39c9407__logo:hover svg path {
    fill: none;
}
#id_a39c9407__logo:hover svg > rect {
    stroke: #000;
    stroke-width: 60;
    rx: 160;
}
#id_a39c9407__logo:hover svg g rect {
    fill: #000;
}
#id_a39c9407__logo:hover svg circle {
    fill: rgb(233, 21, 45);
    stroke: rgb(var(--background));
}
#id_a39c9407 menu {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
}
#id_a39c9407 menu a {
    position: relative;
    display: block;
    text-decoration: none;
    color: inherit;
    padding: 1em;
    font-size: .9em;
    font-weight: 700;
}
#id_a39c9407 menu a::after {
    content: "";
    position: absolute;
    bottom: .5em;
    left: .5em;
    right: .5em;
    height: 1px;
    background-color: currentColor;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}
#id_a39c9407 menu a:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}
#id_a39c9407 menu a:not(:hover)::after {
    transform-origin: right;
}
#id_a39c9407__cta {
    margin-left: 1em;
    background: rgb(var(--box-background));
    border-radius: 1.5em;
    box-shadow: var(--btn-shadow);
    user-select: none;
}
#id_a39c9407__cta::after {
    display: none;
}
#id_a39c9407__cta:hover {
    background: rgb(248, 249, 250);
}
#id_a39c9407__cta:focus {
    box-shadow: none;
}
#id_a39c9407__mobile {
    display: none;
}
@media (max-width: 790px) {
    #id_a39c9407 {
        padding: 0;
    }
    #id_a39c9407 nav {
        padding: 0;
    }
    #id_a39c9407__logo {
        margin-right: 2.3em;
        padding: .7em .9em
    }
    #id_a39c9407 menu {
        display: none;
        padding: 0 1em;
    }
    #id_a39c9407 menu a {
        font-size: 1.5em;
        padding: .3em .7em;
    }
    #id_a39c9407.id_a39c9407__open {
        height: 101vh;
    }
    #id_a39c9407.id_a39c9407__open nav,
    #id_a39c9407.id_a39c9407__open #id_a39c9407__wrapper,
    #id_a39c9407.id_a39c9407__open menu
    {
        display: block;
        display: block
    }
    #id_a39c9407.id_a39c9407__open #id_a39c9407__cta {
        margin-left: 0;
        margin-top: .5em;
        padding: .6em 1.5em;
        text-align: center;
        display: inline-block;
    }
    #id_a39c9407__mobile {
        display: block;
        position: absolute;
        top: .4em;
        right: .4em;
        padding: 1em;
    }
    #id_a39c9407__mobile svg {
        display: block;
    }
}
</style>
<div id="id_a39c9407">
    <nav>
        <div id="id_a39c9407__wrapper">
            <a href="/" id="id_a39c9407__logo">

                <svg width="32" height="32" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="m730.436 0c102.078.00002967 139.094 10.6288 176.413 30.5869 37.318 19.9582 66.606 49.2459 86.564 86.5641 19.957 37.319 30.587 74.335 30.587 176.413v436.872c0 102.078-10.63 139.094-30.587 176.413-19.958 37.318-49.246 66.606-86.564 86.564-37.319 19.957-74.335 30.587-176.413 30.587h-436.872c-102.078 0-139.094-10.63-176.413-30.587-37.3182-19.958-66.6059-49.246-86.5641-86.564-19.6463-36.736-30.252435-73.178-30.5790875-171.676l-.0078125-4.737v-436.872c.00002755-102.078 10.6288-139.094 30.5869-176.413 19.9582-37.3182 49.2459-66.6059 86.5641-86.5641 37.319-19.9581 74.335-30.58687246 176.413-30.5869z" fill="#000"/><rect height="650" rx="120" stroke="#fff" stroke-width="40" width="650" x="174" y="200"/><circle cx="784" cy="240" fill="#ff3b30" r="180" stroke="#000" stroke-width="80"/><g fill="#fff"><rect height="155" rx="40" width="200" x="294" y="320"/><rect height="155" rx="40" width="410" x="294" y="575"/></g></svg>

                Appio
            </a>
            <menu>
                <li>
                    <a href="/#how-it-works">How it works</a>
                </li>
                <li>
                    <a href="/#case-studies">Case studies</a>
                </li>
                <li>
                    <a href="/#pricing">Pricing</a>
                </li>
            </menu>
        </div>
        <menu>
            <li>
                <a href="https://demo.appio.so/">Demo</a>
            </li>
            <li>
                <a id="id_a39c9407__cta" href="/list/">Get started</a>
            </li>
        </menu>
        <div id="id_a39c9407__mobile">
            <svg width="24" height="24" viewBox="0 0 18 18">
                <polyline fill="none" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" points="2 12, 16 12">
                    <animate id="id_a39c9407__bottom-open" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 2 12, 16 12; 2 9, 16 9; 3.5 15, 15 3.5"></animate>
                    <animate id="id_a39c9407__bottom-close" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 3.5 15, 15 3.5; 2 9, 16 9; 2 12, 16 12"></animate>
                </polyline>
                <polyline fill="none" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" points="2 5, 16 5">
                    <animate id="id_a39c9407__top-open" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 2 5, 16 5; 2 9, 16 9; 3.5 3.5, 15 15"></animate>
                    <animate id="id_a39c9407__top-close" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 3.5 3.5, 15 15; 2 9, 16 9; 2 5, 16 5"></animate>
                </polyline>
            </svg>
        </div>
    </nav>
</div>
<script>
    (function () {
        // Scrolling border
        const
            header = document.getElementById("id_a39c9407"),
            scrollClass = "id_a39c9407__scrolled";
        window.addEventListener("scroll", () => {
            if (window.scrollY > 0) {
                header.classList.add(scrollClass);
            } else {
                header.classList.remove(scrollClass);
            }
        });

        // Mobile menu
        let open = false;
        const
            openClass = "id_a39c9407__open",
            mobileMenu = document.getElementById("id_a39c9407__mobile"),
            bottomOpen = document.getElementById("id_a39c9407__bottom-open"),
            bottomClose = document.getElementById("id_a39c9407__bottom-close"),
            topOpen = document.getElementById("id_a39c9407__top-open"),
            topClose = document.getElementById("id_a39c9407__top-close");

        function toggle(state) {
            open = state;
            if (open) {
                header.classList.add(openClass);
                bottomOpen.beginElement();
                topOpen.beginElement();
                document.body.style.overflow = "hidden";
            } else {
                header.classList.remove(openClass);
                bottomClose.beginElement();
                topClose.beginElement();
                document.body.style.overflow = "visible";
            }
        }

        mobileMenu.addEventListener("click", () => {
            toggle(!open)
        });

        header.querySelectorAll("a").forEach(function (a) {
            if (a.getAttribute("href").startsWith("/#")) {
                a.addEventListener("click", function () {
                    toggle(false);
                })
            }
        })
    })();
</script>
<div id="id_f05f6623">
    <style>
#id_ce18b3f6 {
    max-width: var(--content-width);
    padding: 0 var(--content-padding);
    margin: 0 auto;
}
#id_ce18b3f6 menu {
    display: flex;
    gap: 1em;
    list-style: none;
    padding: 0;
    margin: 1em 0 2em;
}
#id_ce18b3f6 h2 {
    scroll-margin-top: var(--scroll-margin-top);
}
#id_ce18b3f6 h2 + h3 {
    margin-top: 1.6em;
}
@media (max-width: 490px) {
    #id_ce18b3f6 h2 {
        font-size: 1.5em;
    }
}
</style>
<script>let e = 'rlpe%gaapl@@taap_p2i(o;.@sgo!'.replace(/.(.)?/g, '$1');</script>
<main id="id_ce18b3f6">
    <menu>
        <li>
            <a href="/legal/terms/">Terms and Conditions</a>
        </li>
        <li>
            <a href="/legal/privacy/">Privacy Policy</a>
        </li>
        <li>
            <a href="/legal/dpa/">Data Processing Addendum</a>
        </li>
    </menu>

    <h1>Privacy Policy</h1>

    <p>Last updated: July 28, 2025</p>

    <br><br>

    <p>
        This Privacy Policy for Appio Limited ("<strong>we</strong>," "<strong>us</strong>," or "<strong>our</strong>"),
        describes how and why we might access, collect, store, use, and/or share ("<strong>process</strong>") your
        personal information when you use our services ("<strong>Services</strong>"), including when you:
    </p>
    <ul>
        <li>
            Visit our website at <a href="https://appio.so">appio.so</a>, or any subdomain, or any other website of ours
            that links to this Privacy Policy
        </li>
        <li>Engage with us in other related ways, including any sales, marketing, or events</li>
    </ul>

    <p>
        <strong>Questions or concerns?</strong> Reading this Privacy Policy will help you understand your privacy rights
        and choices. We are responsible for making decisions about how your personal information is processed. If you do
        not agree with our policies and practices, please do not use our Services.
    </p>

    <h2>SUMMARY OF KEY POINTS</h2>

    <p>
        <strong>This summary provides key points from our Privacy Policy, but you can find out more details about any of these
        topics by clicking the link following each key point or by using our <a href="#toc">table of contents</a>
        below to find the section you are looking for.</strong>
    </p>
    <p>
        <strong>What personal information do we process?</strong>
        When you visit, use, or navigate our Services, we may process personal information depending on how you interact
        with us and the Services, the choices you make, and the products and features you use. Learn more about
        <a href="#personalinfo">personal information you disclose to us</a>.
    </p>
    <p>
        <strong>Do we process any sensitive personal information?</strong>
        Some of the information may be considered "special" or "sensitive" in certain jurisdictions, for example your
        racial or ethnic origins, sexual orientation, and religious beliefs. We do not process sensitive personal
        information.
    </p>
    <p>
        <strong>How do we process your information?</strong>
        We process your information to provide, improve, and administer our Services, communicate with you, for security
        and fraud prevention, and to comply with law. We may also process your information for other purposes with your
        consent. We process your information only when we have a valid legal reason to do so. Learn more about
        <a href="#howweprocess">how we process your information</a>.
    </p>
    <p>
        <strong>In what situations and with which parties do we share personal information?</strong>
        We may share information in specific situations and with specific third parties. Learn more about
        <a href="#whenshared">when and with whom we share your personal information</a>.
    </p>
    <p>
        <strong>How do we keep your information safe?</strong>
        We have adequate organizational and technical processes and procedures in place to protect your personal
        information. However, no electronic transmission over the internet or information storage technology can be
        guaranteed to be 100% secure, so we cannot promise or guarantee that hackers, cybercriminals, or other
        unauthorized third parties will not be able to defeat our security and improperly collect, access, steal,
        or modify your information. Learn more about how we keep your information safe.
    </p>
    <p>
        <strong>What are your rights?</strong>
        Depending on where you are located geographically, the applicable privacy law may mean you have certain rights
        regarding your personal information. Learn more about <a href="#privacyrights">your privacy rights</a>.
    </p>
    <p>
        <strong>How do you exercise your rights?</strong>
        The easiest way to exercise your rights is by contacting us by email at
        <a href="#email" onclick="window.location.href=`mailto:${e}?subject=Privacy Policy`;return false;" title="email"><script>document.write(e)</script></a>. We will consider and act
        upon any request in accordance with applicable data protection laws.
    </p>

    <h2 id="toc">TABLE OF CONTENTS</h2>

    <ul>
        <li><a href="#personalinfo">1. WHAT INFORMATION DO WE COLLECT?</a></li>
        <li><a href="#howweprocess">2. HOW DO WE PROCESS YOUR INFORMATION?</a></li>
        <li><a href="#legalbases">3. WHAT LEGAL BASES DO WE RELY ON TO PROCESS YOUR INFORMATION?</a></li>
        <li><a href="#whenshared">4. WHEN AND WITH WHOM DO WE SHARE YOUR PERSONAL INFORMATION?</a></li>
        <li><a href="#cookies">5. DO WE USE COOKIES AND OTHER TRACKING TECHNOLOGIES?</a></li>
        <li><a href="#sociallogins">6. HOW DO WE HANDLE YOUR SOCIAL LOGINS?</a></li>
        <li><a href="#internationaltransfer">7. IS YOUR INFORMATION TRANSFERRED INTERNATIONALLY?</a></li>
        <li><a href="#retention">8. HOW LONG DO WE KEEP YOUR INFORMATION?</a></li>
        <li><a href="#minors">9. DO WE COLLECT INFORMATION FROM MINORS?</a></li>
        <li><a href="#privacyrights">10. WHAT ARE YOUR PRIVACY RIGHTS?</a></li>
        <li><a href="#donottrack">11. CONTROLS FOR DO-NOT-TRACK FEATURES</a></li>
        <li><a href="#updates">12. DO WE MAKE UPDATES TO THIS POLICY?</a></li>
        <li><a href="#contact">13. HOW CAN YOU CONTACT US ABOUT THIS POLICY?</a></li>
        <li><a href="#reviewupdate">14. HOW CAN YOU REVIEW, UPDATE, OR DELETE THE DATA WE COLLECT FROM YOU?</a></li>
    </ul>

    <h2 id="personalinfo">1. WHAT INFORMATION DO WE COLLECT?</h2>

    <h3>Personal information you disclose to us</h3>

    <p>
        <strong>In Short:</strong> We collect personal information that you provide to us.
    </p>
    <p>
        We collect personal information that you voluntarily provide to us when you register on the Services, express
        an interest in obtaining information about us or our products and Services, when you participate in activities
        on the Services, or otherwise when you contact us by email at
        <a href="#email" onclick="window.location.href=`mailto:${e}?subject=Privacy Policy`;return false;" title="email"><script>document.write(e)</script></a>.
    </p>

    <h3>Information automatically collected</h3>
    <p>
        <strong>In Short:</strong>
        Some information, such as your Internet Protocol (IP) address and/or browser and device characteristics,
        is collected automatically when you visit our Services.
    </p>
    <p>
        We automatically collect certain information when you visit, use, or navigate the Services. This information
        does not reveal your specific identity (like your name or contact information) but may include device and usage
        information, such as your IP address, browser and device characteristics, operating system, language preferences,
        referring URLs, device name, country, location, information about how and when you use our Services, and other
        technical information. This information is primarily needed to maintain the security and operation of our
        Services, and for our internal analytics and reporting purposes.
    </p>
    <p>
        Like many businesses, we also collect information through cookies and similar technologies.
    </p>

    <h2 id="howweprocess">2. HOW DO WE PROCESS YOUR INFORMATION?</h2>
    <p>
        <strong>In Short:</strong> We process your information to provide, improve, and administer our Services,
        communicate with you, for security and fraud prevention, and to comply with law. We may also process your
        information for other purposes with your consent.
    </p>
    <p>
        <strong>We process your personal information for a variety of reasons, depending on how you interact with our
            Services, including:</strong>
    </p>
    <ul>
        <li>
            To facilitate account creation and authentication and otherwise manage user accounts. We may process your
            information so you can create and log in to your account, as well as keep your account in working order.
        </li>
        <li>
            To deliver and facilitate delivery of services to the user. We may process your information to provide you
            with the requested service.
        </li>
        <li>
            To respond to user inquiries/offer support to users. We may process your information to respond to your
            inquiries and solve any potential issues you might have with the requested service.
        </li>
        <li>
            To send administrative information to you. We may process your information to send you details about our
            products and services, changes to our terms and policies, and other similar information.
        </li>
        <li>
            To fulfill and manage your orders. We may process your information to fulfill and manage your orders,
            payments, returns, and exchanges made through the Services.
        </li>
        <li>
            To request feedback. We may process your information when necessary to request feedback and to contact you
            about your use of our Services.
        </li>
        <li>
            To send you marketing and promotional communications. We may process the personal information you send to us
            for our marketing purposes, if this is in accordance with your marketing preferences. You can opt out of our
            marketing emails at any time. For more information, see "<a href="#privacyrights">WHAT ARE YOUR PRIVACY RIGHTS?</a>" below.
        </li>
        <li>
            To deliver targeted advertising to you. We may process your information to develop and display personalized
            content and advertising tailored to your interests, location, and more. For more information see our Cookie Notice.
        </li>
        <li>
            To protect our Services. We may process your information as part of our efforts to keep our Services safe
            and secure, including fraud monitoring and prevention.
        </li>
        <li>
            To identify usage trends. We may process information about how you use our Services to better understand how
            they are being used so we can improve them.
        </li>
        <li>
            To determine the effectiveness of our marketing and promotional campaigns. We may process your information
            to better understand how to provide marketing and promotional campaigns that are most relevant to you.
        </li>
        <li>
            To save or protect an individual's vital interest. We may process your information when necessary to save or
            protect an individual’s vital interest, such as to prevent harm.
        </li>
        <li>
            To manage user accounts. We may use your information for the purposes of managing our account and keeping it
            in working order.
        </li>
        <li>
            To post testimonials. We post testimonials on our Website that may contain personal information. Prior to
            posting a testimonial, we will obtain your consent to use your name and the content of the testimonial.
            If you wish to update, or delete your testimonial, please contact us by email at
            <a href="#email" onclick="window.location.href=`mailto:${e}?subject=Privacy Policy`;return false;" title="email"><script>document.write(e)</script></a>
            and be sure to include your name, testimonial location, and contact information.
        </li>
        <li>
            To enforce our terms, conditions and policies for business purposes, to comply with legal and regulatory
            requirements, or in connection with our contract. Processing is necessary to ensure compliance with our
            service terms and conditions.
        </li>
        <li>
            To respond to legal requests and prevent harm. If we receive a subpoena or other legal request, we may need
            to inspect the data we hold to determine how to respond.
        </li>
    </ul>

    <h2 id="legalbases">3. WHAT LEGAL BASES DO WE RELY ON TO PROCESS YOUR INFORMATION?</h2>
    <p>
        <strong>In Short:</strong>
        We only process your personal information when we believe it is necessary and we have a valid legal reason
        (i.e., legal basis) to do so under applicable law, like with your consent, to comply with laws, to provide you
        with services to enter into or fulfill our contractual obligations, to protect your rights, or to fulfill our
        legitimate business interests.
    </p>

    <p>
        <strong>If you are located in the EU or UK, this section applies to you.</strong>
    </p>
    <p>
        The General Data Protection Regulation (GDPR) and UK GDPR require us to explain the valid legal bases we rely
        on in order to process your personal information. As such, we may rely on the following legal bases to process
        your personal information:
    </p>
    <ul>
        <li>
            Consent. We may process your information if you have given us permission (i.e., consent) to use your personal
            information for a specific purpose. You can withdraw your consent at any time.
        </li>
        <li>
            Performance of a Contract. We may process your personal information when we believe it is necessary to fulfill
            our contractual obligations to you, including providing our Services or at your request prior to entering into a contract with you.
        </li>
        <li>
            Legitimate Interests. We may process your information when we believe it is reasonably necessary to achieve
            our legitimate business interests and those interests do not outweigh your interests and fundamental rights
            and freedoms. For example, we may process your personal information for some of the purposes described in order to:
        </li>
        <li>
            Send users information about special offers and discounts on our products and services
        </li>
        <li>
            Develop and display personalized and relevant advertising content for our users
        </li>
        <li>
            Analyze how our Services are used so we can improve them to engage and retain users
        </li>
        <li>
            Support our marketing activities
        </li>
        <li>
            Diagnose problems and/or prevent fraudulent activities
        </li>
        <li>
            Understand how our users use our products and services so we can improve user experience
        </li>
        <li>
            Enforce our policies for business purposes, legal requirements, and contractual requirements
        </li>
        <li>
            Respond to legal requests
        </li>
        <li>
            Legal Obligations. We may process your information where we believe it is necessary for compliance with our
            legal obligations, such as to cooperate with a law enforcement body or regulatory agency, exercise or defend
            our legal rights, or disclose your information as evidence in litigation in which we are involved.
        </li>
        <li>
            Vital Interests. We may process your information where we believe it is necessary to protect your vital
            interests or the vital interests of a third party, such as situations involving potential threats to the safety of any person.
        </li>
    </ul>
    <p>
        In legal terms, we are generally the "data controller" under European data protection laws of the personal
        information described in this Privacy Notice, since we determine the means and/or purposes of the data processing
        we perform. This Privacy Notice does not apply to the personal information we process as a "data processor" on behalf
        of our customers. In those situations, the customer that we provide services to and with whom we have entered into
        a data processing agreement is the "data controller" responsible for your personal information, and we merely process
        your information on their behalf in accordance with your instructions. If you want to know more about our customers'
        privacy practices, you should read their privacy policies and direct any questions you have to them.
    </p>

    <p>
        <strong>If you are located in Canada, this section applies to you.</strong>
    </p>
    <p>
        We may process your information if you have given us specific permission (i.e., express consent) to use your
        personal information for a specific purpose, or in situations where your permission can be inferred (i.e.,
        implied consent). You can withdraw your consent at any time.
    </p>
    <p>
    In some exceptional cases, we may be legally permitted under applicable law to process your information without your
        consent, including, for example:
    </p>
    <ul>
        <li>
            If collection is clearly in the interests of an individual and consent cannot be obtained in a timely way
        </li>
        <li>
            For investigations and fraud detection and prevention
        </li>
        <li>
            For business transactions provided certain conditions are met
        </li>
        <li>
            If it is contained in a witness statement and the collection is necessary to assess, process, or settle an
            insurance claim
        </li>
        <li>
            For identifying injured, ill, or deceased persons and communicating with next of kin
        </li>
        <li>
            If we have reasonable grounds to believe an individual has been, is, or may be victim of financial abuse
        </li>
        <li>
            If it is reasonable to expect collection and use with consent would compromise the availability or the accuracy
            of the information and the collection is reasonable for purposes related to investigating a breach of an
            agreement or a contravention of the laws of Canada or a province
        </li>
        <li>
            If disclosure is required to comply with a subpoena, warrant, court order, or rules of the court relating
            to the production of records
        </li>
        <li>
            If it was produced by an individual in the course of their employment, business, or profession and the
            collection is consistent with the purposes for which the information was produced
        </li>
        <li>
            If the collection is solely for journalistic, artistic, or literary purposes
        </li>
        <li>
            If the information is publicly available and is specified by the regulations
        </li>
    </ul>
    <p>
        <strong>If you are a resident of California, Colorado, or other US states</strong> with applicable data laws, you may have
        additional privacy rights. Please contact us for details or to exercise your rights.
    </p>


    <h2 id="whenshared">4. WHEN AND WITH WHOM DO WE SHARE YOUR PERSONAL INFORMATION?</h2>
    <p>
        <strong>In Short:</strong> We may share information in specific situations described in this section and/or
        with the following third parties.
    </p>
    <p>
        We may need to share your personal information in the following situations:
    </p>
    <ul>
        <li>
            <strong>Business Transfers.</strong>
            We may share or transfer your information in connection with, or during negotiations of, any merger, sale
            of company assets, financing, or acquisition of all or a portion of our business to another company.
        </li>
        <li>
            <strong>Affiliates.</strong>
            We may share your information with our affiliates, in which case we will require those affiliates to honor
            this Privacy Policy. Affiliates include our parent company and any subsidiaries, joint venture partners,
            or other companies that we control or that are under common control with us.
        </li>
        <li>
            <strong>Business Partners.</strong>
            We may share your information with our business partners to offer you certain products, services, or promotions.
        </li>
    </ul>

    <h2 id="cookies">5. DO WE USE COOKIES AND OTHER TRACKING TECHNOLOGIES?</h2>
    <p>
        <strong>In Short:</strong>
        We may use cookies and other tracking technologies to collect and store your information.
    </p>
    <p>
        We may use cookies and similar tracking technologies (like web beacons and pixels) to gather information when
        you interact with our Services. Some online tracking technologies help us maintain the security of our Services,
        prevent crashes, fix bugs, save your preferences, and assist with basic site functions.
    </p>
    <p>
        We also permit third parties and service providers to use online tracking technologies on our Services for
        analytics and advertising, including to help manage and display advertisements, to tailor advertisements to your
        interests (depending on your communication preferences). The third parties and service providers use their
        technology to provide advertising about products and services tailored to your interests which may appear
        either on our Services or on other websites.
    </p>
    <p>
        Specific information about how we use such technologies and how you can refuse certain cookies is set out in our
        Cookie Notice.
    </p>

    <h2 id="sociallogins">6. HOW DO WE HANDLE YOUR SOCIAL LOGINS?</h2>
    <p>
        <strong>In Short:</strong>
        If you choose to register or log in to our Services using a social media account, we may have access to certain
        information about you.
    </p>
    <p>
        Our Services offer you the ability to register and log in using your third-party social media account details
        (like your GitHub or X logins). Where you choose to do this, we will receive certain profile information about
        you from your social media provider. The profile information we receive may vary depending on the social media
        provider concerned, but will often include your name, email address, and profile picture, as well
        as other information you choose to make public on such a social media platform.
    </p>
    <p>
        We will use the information we receive only for the purposes that are described in this Privacy Policy or that
        are otherwise made clear to you on the relevant Services. Please note that we do not control, and are not
        responsible for, other uses of your personal information by your third-party social media provider. We recommend
        that you review their privacy policy to understand how they collect, use, and share your personal information,
        and how you can set your privacy preferences on their sites and apps.
    </p>

    <h2 id="internationaltransfer">7. IS YOUR INFORMATION TRANSFERRED INTERNATIONALLY?</h2>
    <p>
        <strong>In Short:</strong>
        We may transfer, store, and process your information in countries other than your own.
    </p>
    <p>
        Our servers are located worldwide. If you are accessing our Services, please be aware that your information may
        be transferred to, stored by, and processed by us in our facilities and in the facilities of the third parties
        with whom we may share your personal information
        (see "<a href="#whenshared">WHEN AND WITH WHOM DO WE SHARE YOUR PERSONAL INFORMATION?</a>" above).
    </p>
    <p>
        If you are a resident in the European Economic Area (EEA), United Kingdom (UK), or Switzerland, then these
        countries may not necessarily have data protection laws or other similar laws as comprehensive as those in your
        country. However, we will take all necessary measures to protect your personal information in accordance with
        this Privacy Policy and applicable law.
    </p>

    <h2 id="retention">8. HOW LONG DO WE KEEP YOUR INFORMATION?</h2>
    <p>
        <strong>In Short:</strong> We keep your information for as long as necessary to fulfill the purposes outlined
        in this Privacy Policy unless otherwise required by law.
    </p>
    <p>
        We will only keep your personal information for as long as it is necessary for the purposes set out in this
        Privacy Policy, unless a longer retention period is required or permitted by law (such as tax, accounting,
        or other legal requirements).
    </p>
    <p>
        When we have no ongoing legitimate business need to process your personal information, we will either delete
        or anonymize such information, or, if this is not possible (for example, because your personal information has
        been stored in backup archives), then we will securely store your personal information and isolate it from any
        further processing until deletion is possible.
    </p>

    <h2 id="minors">9. DO WE COLLECT INFORMATION FROM MINORS?</h2>
    <p>
        <strong>In Short:</strong>
        We do not knowingly collect data from or market to children under 18 years of age.
    </p>
    <p>
        We do not knowingly collect, solicit data from, or market to children under 18 years of age, nor do we knowingly
        sell such personal information. By using the Services, you represent that you are at least 18 or that you are the
        parent or guardian of such a minor and consent to such minor dependent’s use of the Services. If we learn that
        personal information from users less than 18 years of age has been collected, we will deactivate the account and
        take reasonable measures to promptly delete such data from our records. If you become aware of any data we may
        have collected from children under age 18, please contact us by email at
        <a href="#email" onclick="window.location.href=`mailto:${e}?subject=Privacy Policy`;return false;" title="email"><script>document.write(e)</script></a>.
    </p>

    <h2 id="privacyrights">10. WHAT ARE YOUR PRIVACY RIGHTS?</h2>
    <p>
        <strong>In Short:</strong>
        You may review, change, or terminate your account at any time, depending on your country, province, or state of
        residence.
    </p>
    <p>
        <strong>Withdrawing your consent:</strong>
        If we are relying on your consent to process your personal information, which may be express and/or implied
        consent depending on the applicable law, you have the right to withdraw your consent at any time. You can
        withdraw your consent at any time by contacting us by using the contact details provided in the section
        "<a href="#contact">HOW CAN YOU CONTACT US ABOUT THIS POLICY?</a>" below.
    </p>
    <p>
        However, please note that this will not affect the lawfulness of the processing before its withdrawal nor, when
        applicable law allows, will it affect the processing of your personal information conducted in reliance on
        lawful processing grounds other than consent.
    </p>

    <h3>Account Information</h3>
    <p>
        If you would at any time like to review or change the information in your account or terminate your account,
        you can: Upon your request to terminate your account, we will deactivate or delete your account and information
        from our active databases. However, we may retain some information in our files to prevent fraud, troubleshoot
        problems, assist with any investigations, enforce our legal terms and/or comply with applicable legal requirements.
    </p>

    <h2 id="donottrack">11. CONTROLS FOR DO-NOT-TRACK FEATURES</h2>
    <p>
        Most web browsers and some mobile operating systems and mobile applications include a Do-Not-Track ("DNT")
        feature or setting you can activate to signal your privacy preference not to have data about your online browsing
        activities monitored and collected. At this stage, no uniform technology standard for recognizing and implementing
        DNT signals has been finalized. As such, we do not currently respond to DNT browser signals or any other mechanism
        that automatically communicates your choice not to be tracked online. If a standard for online tracking is adopted
        that we must follow in the future, we will inform you about that practice in a revised version of this Privacy Policy.
    </p>

    <h2 id="updates">12. DO WE MAKE UPDATES TO THIS POLICY?</h2>
    <p>
        <strong>In Short:</strong>
        Yes, we will update this policy as necessary to stay compliant with relevant laws.
    </p>
    <p>
        We may update this Privacy Policy from time to time. If we make material changes to this Privacy Policy, we may
        notify you either by prominently posting a Policy of such changes or by directly sending you a notification.
        We encourage you to review this Privacy Policy frequently to be informed of how we are protecting your information.
    </p>

    <h2 id="contact">13. HOW CAN YOU CONTACT US ABOUT THIS POLICY?</h2>
    <p>
        If you have questions or comments about this policy, you may contact us by email at
        <a href="#email" onclick="window.location.href=`mailto:${e}?subject=Privacy Policy`;return false;" title="email"><script>document.write(e)</script></a>.
    </p>

    <h2 id="reviewupdate">14. HOW CAN YOU REVIEW, UPDATE, OR DELETE THE DATA WE COLLECT FROM YOU?</h2>
    <p>
        Based on the applicable laws of your country, you may have the right to request access to the personal information
        we collect from you, details about how we have processed it, correct inaccuracies, or delete your personal
        information. You may also have the right to withdraw your consent to our processing of your personal information.
        These rights may be limited in some circumstances by applicable law. To request to review, update, or delete
        your personal information, please contact us by email at
        <a href="#email" onclick="window.location.href=`mailto:${e}?subject=Privacy Policy`;return false;" title="email"><script>document.write(e)</script></a>
        with data subject access request.
    </p>
</main>
</div>
<style>
#id_3e19d9a0__container {
    position: fixed;
    top: 0; bottom: 0;
    left: 0; right: 0;
    z-index: -1;
    color: #b0aea5;
    font-size: .8em;
    background: rgb(var(--outer-background-color));
    padding: 5em 0;
    letter-spacing: 0;
}
#id_3e19d9a0 footer {
    position: fixed;
    bottom: 2em;
    left: 0; right: 0;
}
#id_3e19d9a0 footer > div {
    max-width: var(--content-width);
    margin: 0 auto;
    padding: 0 var(--content-padding);
}
#id_3e19d9a0 menu {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
#id_3e19d9a0 menu:last-of-type {
    padding-bottom: 0.5em;
    border-bottom: solid 1px rgba(176, 174, 165, .2);
}
#id_3e19d9a0 a,
#id_3e19d9a0 span
{
    position: relative;
    display: block;
    padding: .5em 1em;
    text-decoration: none;
    color: #b0aea5;
    white-space: nowrap;
}
#id_3e19d9a0 a:hover {
    text-decoration: underline;
}
#id_3e19d9a0 span {
    opacity: .6;
}
#id_3e19d9a0 span::after {
    content: "→";
    font-size: .8em;
    margin-left: .3em;
}
#id_3e19d9a0 aside {
    padding-left: 1em;
    font-size: .85em;
    max-width: var(--content-width);
    margin: 1em auto;
}
#id_3e19d9a0 a[target]::after {
    position: absolute;
    content: "";
    display: inline-block;
    top: 0.5em;
    right: 0;
    border: 0.25em solid transparent;
    border-bottom-color: currentColor;
    transform: rotate(45deg);
    opacity: .5;
}
#id_3e19d9a0 a[target]:hover::after {
    opacity: 1;
}
</style>
<div id="id_3e19d9a0">
    <div id="id_3e19d9a0__container">
        <footer>
            <div>
                <menu>
                    <li>
                        <a href="/blog/">Blog</a>
                    </li>
                    <li>
                        <a href="/case-studies/">Case studies</a>
                    </li>
            
            
            
                    <li>
                        <a href="/contact/">Contact</a>
                    </li>
                    <li>
                        <a href="/legal">Legal</a>
                    </li>
                    <li>
                        <a href="https://docs.appio.so/" target="_blank">Docs</a>
                    </li>
                    <li>
                        <a href="https://status.appio.so/" target="_blank" rel="noopener noreferrer">Status</a>
                    </li>
                </menu>
                <menu>
                    <li>
                        <span>Integrations</span>
                    </li>
                    <li>
                        <a href="/integrations/zapier/">Zapier</a>
                    </li>
                </menu>
                <aside>
                    © 2025 Appio Limited. All rights reserved.
                </aside>
            </div>
        </footer>
    </div>
</div>




</body>
</html>
