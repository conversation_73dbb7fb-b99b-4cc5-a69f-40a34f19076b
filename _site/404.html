
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Page Not Found</title>

    <meta name="author" content="Appio.so">
    <meta name="description" content="A<PERSON><PERSON> adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">

    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Open graph -->
    <meta property="og:url" content="https://appio.so">
    <meta property="og:type" content="website">
    <meta property="og:title" content="Appio: Mobile Features for Your Web App">
    <meta property="og:description" content="Appio adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta property="og:image" content="/assets/images/social.png">

    <!-- Twitter -->
    <meta name="twitter:url" content="https://appio.so">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@appio_so">
    <meta name="twitter:creator" content="@appio_so">
    <meta name="twitter:title" content="Appio: Mobile Features for Your Web App">
    <meta name="twitter:description" content="Appio adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores.">
    <meta name="twitter:image" content="/assets/images/social.png">
    <meta name="twitter:image:src" content="/assets/images/social.png">

    <style>@charset "UTF-8";

:root {
    --outer-background-color: 20,20,20;
    /*--background: 255,255,255;*/
    /*--box-background: 245,245,247;*/
    --background: 245,245,247;
    --box-background: 255,255,255;
    --text-color: 20,20,20;
    --border-color: 233,236,239;
    --content-width: 1200px;
    --content-padding: 1rem;
    --section-gap: 8rem;
    --scroll-margin-top: 7rem;
    --btn-color: 1,122,255;
    --btn-hover-color: 1,113,227;
    /*--btn-color: 1,113,227;*/
    /*--btn-hover-color: 0, 102, 204;*/
    --btn-shadow: inset 0 0 2px 0 #fcfcfc, 0 1px 1px 1px #d1d1d1;
    --box-shadow: rgba(13, 19, 27, 0.25) 0 0 1px 0, rgba(13, 19, 27, 0.05) 0 2px 1px 0;
    --font: "SF Pro Display", "SF Pro Icons", "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;

    /*--frame-border-width: 10;*/
    /*--frame-border-radius: 15;*/

    font-feature-settings: "liga" 1, "calt" 1; /* fix for Chrome */
}
@supports (font-variation-settings: normal) {
    :root {
        --font: "SF Pro Display", "SF Pro Icons", "InterVariable", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
    }
}
@media (max-width: 1300px) {
    :root {
        --content-width: auto;
        --content-padding: 3rem;
    }
}
@media (max-width: 790px) {
    :root {
        --content-padding: 1.5rem;
        --section-gap: 5rem;
        --scroll-margin-top: 5.4rem;
    }
}

section {
    max-width: var(--content-width);
    margin: auto;
    padding: 0 var(--content-padding);
}

html {
    font-family: var(--font);
    font-size: 106.25%;
    quotes: "“" "”";
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;

    /*background: rgb(var(--outer-background-color));*/
    /*scrollbar-color: rgb(255,255,255/.3) rgb(var(--outer-background-color));*/
}

html,
body
{
    /* prevent bouncy scroll on MacOS and iOS */
    overscroll-behavior: none;
}

/*html::before,*/
/*html::after {*/
/*    content: "";*/
/*    position: fixed;*/
/*    z-index: 100000;*/
/*    top: 0;*/
/*    bottom: 0;*/
/*    left: 0;*/
/*    right: 0;*/
/*    border: solid calc(var(--frame-border-width) * 1px) rgb(var(--outer-background-color));*/
/*    pointer-events: none;*/
/*}*/
/*html::after {*/
/*    z-index: 100001;*/
/*    border-radius: calc((var(--frame-border-width) + var(--frame-border-radius)) * 1px);*/
/*}*/

*, *::before, *::after {
    box-sizing: border-box;
}

html,body,ul,ol,li,dl,dt,dd,h1,h2,h3,h4,h5,h6,hgroup,p,blockquote,figure,form,fieldset,input,legend,pre,abbr,button {
    margin: 0;
    padding: 0;
}

dt {
    font-weight: 700;
    margin-top: 1em;
}

pre,code,address,caption,th,figcaption {
    font-size: 1em;
    font-weight: normal;
    font-style: normal;
}

code {
    font-family: "SF Mono", SFMono-Regular, ui-monospace, Menlo, monospace;
    font-weight: inherit;
    letter-spacing: 0;
    display: block;
    padding: 1em;
}

fieldset,iframe {
    border: 0;
}

caption,th {
    text-align: left;
}

main,summary,details {
    display: block;
}

audio,canvas,video,progress {
    vertical-align: baseline;
}

body {
    font-size: 1rem;
    line-height: 1.4705882353;
    font-weight: 400;
    letter-spacing: -0.022em;
    font-family: var(--font);
    background-color: rgb(var(--background));
    color: rgb(var(--text-color));
    font-style: normal;
    min-height: 100vh; /* to make background fill the whole screen */
}

body,button,input,textarea,select {
    font-synthesis: none;
    -moz-font-feature-settings: "kern";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

textarea {
    color: rgb(var(--text-color));
}

h1,h2,h3,h4,h5,h6 {
    font-weight: 700;
    color: rgb(var(--text-color));
    font-family: var(--font);
}

h1 img,h2 img,h3 img,h4 img,h5 img,h6 img {
    display: block;
    margin: 0;
}

h1+*,h2+*,h3+*,h4+*,h5+*,h6+* {
    margin-top: 0.8em;
}

h1+h1,h1+h2,h1+h3,h1+h4,h1+h5,h1+h6,h2+h1,h2+h2,h2+h3,h2+h4,h2+h5,h2+h6,h3+h1,h3+h2,h3+h3,h3+h4,h3+h5,h3+h6,h4+h1,h4+h2,h4+h3,h4+h4,h4+h5,h4+h6,h5+h1,h5+h2,h5+h3,h5+h4,h5+h5,h5+h6,h6+h1,h6+h2,h6+h3,h6+h4,h6+h5,h6+h6 {
    margin-top: 0.4em;
}

p+h1,ul+h1,ol+h1,p+h2,ul+h2,ol+h2,p+h3,ul+h3,ol+h3,p+h4,ul+h4,ol+h4,p+h5,ul+h5,ol+h5,p+h6,ul+h6,ol+h6 {
    margin-top: 1.6em;
}

p+*,ul+*,ol+*,dl+* {
    margin-top: 0.8em;
}

ul,ol {
    margin-inline-start:1.1764705882em;
}

ul ul,ul ol,ol ul,ol ol {
    margin-top: 0;
    margin-bottom: 0;
}

nav ul,nav ol {
    margin: 0;
    list-style: none;
}

li li {
    font-size: 1em;
}

b,strong {
    font-weight: 700;
}

em,i,cite,dfn {
    font-style: italic;
}

abbr {
    border: 0;
}

a {
    color: rgb(var(--text-color));
    letter-spacing: inherit;
}

/* a:link,a:visited,a:active,a:disabled {} */

a:active {
    text-decoration: none;
}

a.disabled,a :disabled {
    opacity: 0.42;
}

p+a {
    display: inline-block
}

sup,sub {
    position: relative;
    font-size: 0.6em;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

hr {
    border: none;
    height: 1px;
    background-color: rgb(var(--border-color));
    margin: 3em 0;
}

.nowrap {
    display: inline-block;
    text-decoration: inherit;
    white-space: nowrap;
}

.clear {
    clear: both;
}

h1 {
    font-size: 2.8235294118em;
    line-height: 1.1;
}

h2 {
    font-size: 1.8823529412em;
    line-height: 1.125;
    letter-spacing: .004em;
}

h3 {
    font-size: 1.6470588235em;
    line-height: 1.1428571429;
    letter-spacing: .007em;
}

h4 {
    font-size: 1.4117647059em;
    line-height: 1.1666666667;
    letter-spacing: .009em;
}

h5 {
    font-size: 1.2941176471em;
    line-height: 1.1818181818;
    letter-spacing: .01em;
}

h6 {
    font-size: 1em;
    line-height: 1.4705882353;
    letter-spacing: -.022em;
}

p {
    /* font-size: 1.1176470588em; */
    /* line-height: 1.4211026316; */
    font-weight: 400;
    letter-spacing: .012em;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
    min-width: 100%;
    font-size: .8235294118em;
    line-height: 1.4211026316;
    font-weight: 400;
    letter-spacing: -.016em;
    border-style: hidden;
    empty-cells: hide;
}

td, th {
    width: 1%;
    -webkit-hyphens: auto;
    hyphens: auto;
    min-width: 10em;
    border-style: solid;
    border-width: 1px 0;
    padding: .5882352941em;
}

th {
    border-color: rgb(var(--border-color));
    font-weight: 600;
    word-break: keep-all;
}

td {
    border-color: rgb(var(--border-color));
    word-break: break-word;
    color: var(--text-color);
}

/* Global animations */
/* clean-css ignore:start */
@keyframes fadeIn {
    from {
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
@keyframes arrowFly {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    50% {
        transform: translateX(0.5em);
        opacity: 0;
    }
    51% {
        transform: translateX(-0.5em);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}
/* clean-css ignore:end */</style>
    <style>/* Variable fonts usage:
:root { font-family: "Inter", sans-serif; }
@supports (font-variation-settings: normal) {
  :root { font-family: "InterVariable", sans-serif; font-optical-sizing: auto; }
} */
@font-face {
  font-family: InterVariable;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("/assets/fonts/inter-4-1/InterVariable.woff2") format("woff2");
}
@font-face {
  font-family: InterVariable;
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url("/assets/fonts/inter-4-1/InterVariable-Italic.woff2") format("woff2");
}

/* static fonts */
@font-face { font-family: "Inter"; font-style: normal; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Thin.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ThinItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraLight.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraLightItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Light.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-LightItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Regular.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Italic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Medium.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-MediumItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-SemiBold.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-SemiBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Bold.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-BoldItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraBold.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-ExtraBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: normal; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-Black.woff2") format("woff2"); }
@font-face { font-family: "Inter"; font-style: italic; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/Inter-BlackItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Thin.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 100; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ThinItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraLight.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 200; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraLightItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Light.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 300; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-LightItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Regular.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 400; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Italic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Medium.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 500; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-MediumItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-SemiBold.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 600; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-SemiBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Bold.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 700; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-BoldItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraBold.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 800; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-ExtraBoldItalic.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: normal; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-Black.woff2") format("woff2"); }
@font-face { font-family: "InterDisplay"; font-style: italic; font-weight: 900; font-display: swap; src: url("/assets/fonts/inter-4-1/InterDisplay-BlackItalic.woff2") format("woff2"); }

@font-feature-values InterVariable {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
@font-feature-values Inter {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
@font-feature-values InterDisplay {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
</style>

    <!-- Fonts -->
    <link rel="preload" href="/assets/fonts/inter-4-1/InterVariable.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <script>
        // Smooth scrolling only for on-page links. Instant scroll for external and back links
        window.addEventListener("load", () => {
            document.documentElement.style.scrollBehavior = "smooth";
        });

        // Adding class to elements in viewport
        window.viewPortObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add("js-in-viewport");
                    window.viewPortObserver.unobserve(entry.target);
                }
            });
        });
    </script>
</head>
<body id="top">

<style>
    main {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
    }
    div {
        padding: 2em;
    }
</style>
<main>
    <div>
        
<h1>Page Not Found</h1>

<p>Oops! The page you are looking for does not exist.</p>

<a href="/">← Back to Homepage</a>
    </div>
</main>




</body>
</html>
