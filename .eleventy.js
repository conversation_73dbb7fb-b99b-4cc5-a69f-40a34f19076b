const htmlmin = require("html-minifier-terser");

module.exports = function(eleventyConfig) {
    const isDevelopment = process.env.NODE_ENV === "dev";

    // Expose to all tempaltes
    eleventyConfig.addGlobalData("isDevelopment", isDevelopment);

    // Add HTML minification transform only in production
    if (! isDevelopment) {
        eleventyConfig.addTransform("htmlmin", function(content, outputPath) {
            if (outputPath && outputPath.endsWith(".html")) {
                return htmlmin.minify(content, {
                    removeComments: true,

                    // Whitespace handling
                    collapseWhitespace: true,
                    conservativeCollapse: false,
                    preserveLineBreaks: false,

                    // Attribute optimization
                    removeEmptyAttributes: true,
                    removeRedundantAttributes: true,
                    removeAttributeQuotes: true,

                    // HTML optimization
                    useShortDoctype: true,
                    removeEmptyElements: false, // Keep this false to avoid removing elements with CSS content
                    minifyCSS: true,
                    minifyJS: true,

                    // Advanced optimizations
                    removeOptionalTags: false, // Keep this false for better compatibility
                    removeScriptTypeAttributes: true,
                    removeStyleLinkTypeAttributes: true,
                    sortAttributes: true,
                    sortClassName: true
                });
            }
            return content;
        });
    }

    // Add date filter for sitemap
    eleventyConfig.addFilter("dateToISO", function(date) {
        if (!date) return new Date().toISOString().split("T")[0];
        return new Date(date).toISOString().split("T")[0];
    });

    // Add date filter for general date formatting
    eleventyConfig.addFilter("date", function(dateObj, format) {
        if (dateObj === "now") {
            dateObj = new Date();
        }
        if (format === "YYYY") {
            return new Date(dateObj).getFullYear().toString();
        }
        return new Date(dateObj).toISOString().split("T")[0];
    });

    // Add smart date filter for blog posts with conditional formatting
    eleventyConfig.addFilter("smartDate", function(dateObj, expanded) {
        if (!dateObj) return "";

        const date = new Date(dateObj);
        const now = new Date();
        const diffTime = now - date;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        // Helper function to get season
        function getSeason(date) {
            const month = date.getMonth();
            if (month >= 0 && month <= 2) return "Q1";
            if (month >= 3 && month <= 5) return "Q2";
            if (month >= 6 && month <= 8) return "Q3";
            return "Q4";
        }

        // Helper function to get month name
        function getMonthName(date) {
            const months = [
                "January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"
            ];
            return months[date.getMonth()];
        }

        const year = date.getFullYear();

        // Output formats:
        let outputPrefix = "";
        if (expanded) {
            outputPrefix = "Published on "
        }

        // Within 30 days: "January 1, 2025"
        if (diffDays <= 30) {
            return `${outputPrefix}${getMonthName(date)} ${date.getDate()}, ${year}`;
        }

        if (expanded) {
            outputPrefix = "Published in "
        }

        // Older than 30 days but within 3 months (90 days): "January, 2025"
        if (diffDays <= 90) {
            return `${outputPrefix}${getMonthName(date)}, ${year}`;
        }

        // Older than 3 months but within 12 months (365 days): "Winter, 2025"
        if (diffDays <= 365) {
            return `${outputPrefix}${getSeason(date)}, ${year}`;
        }

        if (expanded) {
            outputPrefix = "Published in year "
        } else {
            outputPrefix = "Year "
        }

        // Older than 12 months: "Year 2025"
        return `${outputPrefix}${year.toString()}`;
    });

    // Add random ID generator filter with collision prevention
    const generatedIds = [];
    eleventyConfig.addFilter("componentId", function() {
        let id;
        do {
            id = "id_" + crypto.randomUUID().split("-")[0];
        } while (generatedIds.includes(id));
        generatedIds.push(id);
        return id;
    });

    // Add collection for case studies
    eleventyConfig.addCollection("caseStudies", function(collection) {
        return collection.getFilteredByTag("case-study").sort(function(a, b) {
            return (a.data.order || 0) - (b.data.order || 0);
        });
    });

    // Add collection for blog posts
    eleventyConfig.addCollection("blogPosts", function(collection) {
        return collection.getFilteredByTag("blog").sort(function(a, b) {
            return new Date(b.data.date) - new Date(a.data.date);
        });
    });

    // Copy static assets
    eleventyConfig.addPassthroughCopy("src/assets");

    // Copy individual files
    eleventyConfig.addPassthroughCopy("src/apple-touch-icon.png");
    eleventyConfig.addPassthroughCopy("src/favicon.ico");
    eleventyConfig.addPassthroughCopy("src/favicon.svg");
    eleventyConfig.addPassthroughCopy("src/favicon-96x96.png");
    eleventyConfig.addPassthroughCopy("src/robots.txt");
    eleventyConfig.addPassthroughCopy("src/site.webmanifest");
    eleventyConfig.addPassthroughCopy("src/web-app-manifest-192x192.png");
    eleventyConfig.addPassthroughCopy("src/web-app-manifest-512x512.png");
    eleventyConfig.addPassthroughCopy("src/_redirects");

    // Set input and output directories
    return {
        dir: {
            input: "src",
            output: "_site",
            includes: "_includes",
            layouts: "_includes"
        },
        templateFormats: ["html", "md", "njk"],
        htmlTemplateEngine: "njk",
        markdownTemplateEngine: "njk"
    };
};