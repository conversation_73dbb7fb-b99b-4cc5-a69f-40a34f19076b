---
layout: layouts/main_layout.njk
title: "Appio: Contact"
---
{%- set id = "" | componentId() -%}
<style>
#{{ id }} {
    max-width: var(--content-width);
    padding: 0 var(--content-padding);
    margin: 0 auto;
}
#{{ id }} section {
    background: rgb(var(--box-background));
    box-shadow: var(--box-shadow);
    border-radius: 1.5em;
    padding: 1em;
    display: flex;
    width: 100%;
    min-height: 50vh;
}
#{{ id }} section > div {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}
#{{ id }} section > div:first-child {
    border-right: 1px solid rgb(var(--border-color));
}
#{{ id }} h1 {
    text-align: center;
    padding: .5em;
}
#{{ id }} svg {
    display: block;
    height: 1em;
    width: 1em;
    padding-bottom: .2em;
}
#{{ id }} a {
    display: block;
    text-decoration: none;
    font-size: 2em;
    padding: 2em;
}
#{{ id }} a:hover {
    fill: rgb(var(--btn-color));
    color: rgb(var(--btn-color));
}
@media (max-width: 790px) {
    #{{ id }} section {
        flex-direction: column;
    }
    #{{ id }} section a {
        width: 100%;
        padding: 1em;
    }
    #{{ id }} section > div:first-child {
        border-right: none;
        border-bottom: 1px solid rgb(var(--border-color));
    }
    #{{ id }} h1 {
        text-align: left;
    }
}
</style>
<main id="{{ id }}">
    <h1>Contact</h1>

    <div>
        <section>
            <div>
                <script>let e = 'rhpi@@taap_p2i(o;.@sgo!'.replace(/.(.)?/g, '$1');</script>
                <a href="#email" onclick="window.location.href=`mailto:${e}`;return false;" title="email">
                    {% include "./assets/symbols/mail.svg" %}
                    <script>document.write(e)</script>
                </a>
            </div>
            <div>
                <a href="https://cal.appio.so" title="calendar" target="_blank">
                    {% include "./assets/symbols/cal-17.svg" %}
                    Book a call
                </a>
            </div>
        </section>
    </div>
</main>