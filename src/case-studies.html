---
layout: layouts/main_layout.njk
title: "Appio: Case Studies"
---
{%- set id = "" | componentId() -%}
<style>
#{{ id }} {
    max-width: var(--content-width);
    margin: 0 auto;
    padding: 0 var(--content-padding);
}
#{{ id }} header {
    margin: 0 1.3em;
}
#{{ id }} header span {
    font-weight: 600;
    padding: 0 .15em;
}
#{{ id }} header span.{{ id }}__illustrative {
    color: rgb(86, 1, 89);
    background: rgb(249, 226, 251);
}
#{{ id }} header span.{{ id }}__real {
    color: #003d5a;
    background: #c9f0ff;
}
#{{ id }} main {
    margin-top: 2em;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3em;
}
#{{ id }} main a {
    position: relative;
    display: block;
    overflow: hidden;
    background: rgb(var(--box-background));
    box-shadow: var(--box-shadow);
    padding: 2.5em 2.5em 5em;
    border-radius: 1em;
    text-decoration: none;
}
#{{ id }} main a.{{ id }}__illustrative::after {
    content: "illustrative";
    position: absolute;
    top: 1em;
    right: -2em;
    transform: rotate(45deg);
    color: rgb(86, 1, 89);
    background: rgb(249, 226, 251);
    padding: .2em 2em;
    font-weight: 500;
}
#{{ id }} main a:hover {
    transform: translateY(-.1em);
    box-shadow: var(--box-shadow), rgba(13, 19, 27, .1) 0 10px 30px 0;
    transition: box-shadow .2s ease, transform .2s ease;
}
#{{ id }} main a img {
    width: 50%;
    padding-bottom: .5em;
}
#{{ id }} main a h2 {
    font-size: 2em;
}
#{{ id }} main a:hover h2 {
    color: rgb(var(--btn-color));
}
#{{ id }} main a span {
    font-weight: 700;
    position: absolute;
    bottom: 2em;
    left: 2.5em;
}
#{{ id }} main a:hover span {
    color: rgb(var(--btn-color));
}
#{{ id }} main a span::after {
    content: "→";
    display: inline-block;
    margin-left: .25em;
    transition: transform 0.3s ease, opacity 0.3s ease;
    animation: none;
    font-weight: 500;
}
#{{ id }} main a:hover span::after {
    animation: arrowFly 0.6s ease forwards;
}
@media (max-width: 790px) {
    #{{ id }} header {
        margin: 1em .5em 0;
    }
    #{{ id }} main {
        gap: 1.5em;
    }
    #{{ id }} main a {
        padding: 1.5em 1.5em 4em;
    }
    #{{ id }} main a span {
        left: 1.5em;
        bottom: 1.5em;
    }
}
@media (max-width: 590px) {
    #{{ id }} main {
        grid-template-columns: repeat(1, 1fr);
    }
}
</style>
<div id="{{ id }}">
    <header>
        <h1>Case studies</h1>
        <p>
            Some of these examples are <span class="{{ id }}__illustrative">illustrative</span>,
            inspired by <span class="{{ id }}__real">real product needs</span>.
            They show how Appio can add value today.
        </p>
    </header>
    <main>
        {%- for caseStudy in collections.caseStudies %}
        <a href="{{ caseStudy.url }}" {% if not caseStudy.data.real %}class="{{ id }}__illustrative"{% endif %}>
            {% if caseStudy.data.logo %}
            <img src="/assets/logos/{{ caseStudy.data.logo }}" alt="{{ caseStudy.data.title }}">
            {% else %}
            <h2>{{ caseStudy.data.company }}</h2>
            {% endif %}
            <p>{{ caseStudy.data.description }}</p>
            <span>Read more</span>
        </a>
        {%- endfor %}
    </main>

    {% include "partials/page_footer.njk" %}
</div>