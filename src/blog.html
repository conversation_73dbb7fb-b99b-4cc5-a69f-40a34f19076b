---
layout: layouts/main_layout.njk
title: "Appio: Blog"
---
{%- set id = "" | componentId() -%}
<style>
#{{ id }} {
    max-width: var(--content-width);
    margin: 0 auto;
    padding: 0 var(--content-padding);
}
#{{ id }} header {
    margin: 0 1.3em;
}
#{{ id }} main {
    margin-top: 2em;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3em;
}
#{{ id }} main a {
    position: relative;
    display: block;
    overflow: hidden;
    background: rgb(var(--box-background));
    box-shadow: var(--box-shadow);
    padding: 1em 1em 3em;
    border-radius: 1em;
    text-decoration: none;
}
#{{ id }} main a:hover {
    transform: translateY(-.1em);
    box-shadow: var(--box-shadow), rgba(13, 19, 27, .1) 0 10px 30px 0;
    transition: box-shadow .2s ease, transform .2s ease;
}
#{{ id }} main a img,
#{{ id }}__placeholder
{
    position: relative;
    display: block;
    width: 100%;
    aspect-ratio: 16 / 9;
    border-radius: .75em;
    background-color: rgb(var(--border-color));
}
#{{ id }}__placeholder::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2em;
    height: 2em;
    background: url("/assets/images/appio-bw.svg") no-repeat center/contain;
    opacity: .3;
}
#{{ id }} main div {
    padding: 0 .5em;
}
#{{ id }} main a time {
    display: block;
    margin-top: .7em;
    font-size: .8em;
    opacity: .4;
}
#{{ id }} main a h2 {
    margin-top: .2em;
    font-size: 1.5em;
}
#{{ id }} main a span {
    font-weight: 500;
    position: absolute;
    bottom: 1.2em;
}
#{{ id }} main a:hover span {
    color: rgb(var(--btn-color));
}
#{{ id }} main a span::after {
    content: "→";
    display: inline-block;
    margin-left: .25em;
    transition: transform 0.3s ease, opacity 0.3s ease;
    animation: none;
    font-weight: 500;
}
#{{ id }} main a:hover span::after {
    animation: arrowFly 0.6s ease forwards;
}
@media (max-width: 790px) {
    #{{ id }} header {
        margin: 1em .5em 0;
    }
    #{{ id }} main {
        gap: 1.5em;
        grid-template-columns: repeat(1, 1fr);
    }
}

</style>
<div id="{{ id }}">
    <header>
        <h1>Appio Blog</h1>
    </header>
    <main>
        {%- for post in collections.blogPosts %}
        <a href="{{ post.url }}">
            {% if post.data.image %}
                <img src="/assets/blog/{{ post.data.image }}" alt="{{ post.data.title }}">
            {% else %}
                <div id="{{ id }}__placeholder"></div>
            {% endif %}
            <div>
                <time datetime="{{ post.data.date }}">{{ post.data.date | smartDate }}</time>
                <h2>{{ post.data.title }}</h2>
                <span>Read more</span>
            </div>
        </a>
        {%- endfor %}
    </main>

    {% include "partials/page_footer.njk" %}
</div>