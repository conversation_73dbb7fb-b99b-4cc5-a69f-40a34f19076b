---
layout: layouts/page_layout.njk
---
{%- set id = "" | componentId() -%}
<style>
#{{ id }} header {
    margin-top: 3em;
}

#{{ id }} main h1 {
    display: none;
}

#{{ id }} header img {
    width: 50%;
}

#{{ id }} main {
    margin: 2em 0;
}

#{{ id }} .note {
    padding-bottom: 1em;
    border-bottom: solid 1px rgb(var(--border-color));
}
#{{ id }} .note span {
    font-weight: 600;
    text-transform: capitalize;
}
#{{ id }} .note span.{{ id }}__real {
    color: rgb(0, 61, 90);
    background: rgb(201, 240, 255);
}
#{{ id }} .note span.{{ id }}__illustrative {
    color: rgb(86, 1, 89);
    background: rgb(249, 226, 251);
}

@media (max-width: 790px) {
    #{{ id }} header {
        margin-top: 2em;
    }
}
</style>
<div id="{{ id }}">
    {% if not real %}
        <div class="note">
            This <span class="{{ id }}__illustrative">illustrative</span> example,
            inspired by <span class="{{ id }}__real">real product needs</span>,
            shows how <PERSON><PERSON><PERSON> can add value today.
        </div>
    {% endif %}

    <header>
        {% if logo %}
            <img src="/assets/logos/{{ logo }}" alt="{{ title }}">
        {% else %}
            <h1>{{ title }}</h1>
        {% endif %}
    </header>

    <main>
        {{ content | safe }}
    </main>
</div>
