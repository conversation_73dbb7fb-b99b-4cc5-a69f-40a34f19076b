---
__title: "Appio: Mobile Features for Your Web App"
__description: "<PERSON><PERSON><PERSON> adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores."
title: "Appio: Mobile Features for Your Web App"
description: "<PERSON><PERSON><PERSON> adds push notifications and home screen widgets to your web app, without building or maintaining mobile apps, hiring developers, or dealing with app stores."
---

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>{{ title }}</title>

    <meta name="author" content="Appio.so">
    <meta name="description" content="{{ description }}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">

    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Open graph -->
    <meta property="og:url" content="https://appio.so">
    <meta property="og:type" content="website">
    <meta property="og:title" content="{{ __title }}">
    <meta property="og:description" content="{{ __description }}">
    <meta property="og:image" content="/assets/images/social.png">

    <!-- Twitter -->
    <meta name="twitter:url" content="https://appio.so">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@appio_so">
    <meta name="twitter:creator" content="@appio_so">
    <meta name="twitter:title" content="{{ __title }}">
    <meta name="twitter:description" content="{{ __description }}">
    <meta name="twitter:image" content="/assets/images/social.png">
    <meta name="twitter:image:src" content="/assets/images/social.png">

    <style>{% include "../../assets/styles.css" %}</style>
    <style>{% include "../../assets/fonts/inter-4-1/inter.css" %}</style>

    <!-- Fonts -->
    <link rel="preload" href="/assets/fonts/inter-4-1/InterVariable.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <script>
        // Smooth scrolling only for on-page links. Instant scroll for external and back links
        window.addEventListener("load", () => {
            document.documentElement.style.scrollBehavior = "smooth";
        });

        // Adding class to elements in viewport
        window.viewPortObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add("js-in-viewport");
                    window.viewPortObserver.unobserve(entry.target);
                }
            });
        });
    </script>
</head>
<body id="top">

{{ content | safe }}

{% include "../partials/posthog.njk" %}

</body>
</html>
