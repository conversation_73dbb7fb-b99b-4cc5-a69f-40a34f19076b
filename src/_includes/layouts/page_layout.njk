---
layout: layouts/layout.njk
---
{%- set id = "" | componentId() -%}
<style>
:root {
    --background: 255,255,255;
}
#{{ id }} {
    position: relative;
    padding-top: 7em;
    background-color: rgb(var(--background));
    margin-bottom: 14em; /* footer height + rounded corners bar. allow for 2 lines on mobile */
    padding-bottom: 3em;
    min-height: 100vh;
}
#{{ id }}::before {
     content: "";
     background: rgb(var(--background));
     display: block;
     position: absolute;
     height: 3em;
     bottom: -1.5em;
     border-radius: calc(var(--frame-border-radius, 20) * 1px);;
     left: calc(var(--frame-border-width, 0) * 1px);
     right: calc(var(--frame-border-width, 0) * 1px);
}
#{{ id }}__content {
    max-width: var(--content-width);
    padding: 0 var(--content-padding);
    margin: 0 auto;
}
@media (max-width: 790px) {
    #{{ id }} {
        padding-top: 5em;
    }
}
</style>

{% include "../partials/menu.njk" %}

<div id="{{ id }}">
    <div id="{{ id }}__content">
        {{ content | safe }}

        {% include "../partials/page_footer.njk" %}
    </div>
</div>

{% include "../partials/footer.njk" %}