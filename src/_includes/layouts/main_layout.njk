---
layout: layouts/layout.njk
---
{%- set id = "" | componentId() -%}
<style>
#{{ id }} {
    position: relative;
    padding-top: 6em;
    background: rgb(var(--background));
    margin-bottom: 14em; /* footer height + rounded corners bar. allow for 2 lines on mobile */
    padding-bottom: 3em;
    min-height: 100vh;
}
#{{ id }}::before {
    content: "";
    background: rgb(var(--background));
    display: block;
    position: absolute;
    height: 3em;
    bottom: -1.5em;
    border-radius: calc(var(--frame-border-radius, 20) * 1px);;
    left: calc(var(--frame-border-width, 0) * 1px);
    right: calc(var(--frame-border-width, 0) * 1px);
}
@media (max-width: 790px) {
    #{{ id }} {
        padding-top: 4em;
    }
}
</style>
{% include "../partials/menu.njk" %}
<div id="{{ id }}">
    {{ content | safe }}
</div>
{% include "../partials/footer.njk" %}
