---
layout: layouts/page_layout.njk
---
{%- set id = "" | componentId() -%}
<style>
#{{ id }} time {
    opacity: .6;
    font-weight: 500;
}
#{{ id }} h1 {
    margin-top: .2em;
}
#{{ id }} img,
#{{ id }}__placeholder
{
    position: relative;
    display: block;
    width: 100%;
    max-width: 40em;
    aspect-ratio: 16 / 9;
    border-radius: .75em;
    background-color: rgb(var(--border-color));
}
#{{ id }}__placeholder::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2em;
    height: 2em;
    background: url("/assets/images/appio-bw.svg") no-repeat center/contain;
    opacity: .3;
}
#{{ id }} > main {
    margin-top: 2em;
}
@media (max-width: 790px) {
    #{{ id }} > main {
        margin-top: 1em;
    }
}
</style>
<div id="{{ id }}">
    <time datetime="{{ date }}">{{ date | smartDate(true) }}</time>

    <h1>{{ title }}</h1>

    {% if post.data.image %}
        <img src="/assets/blog/{{ post.data.image }}" alt="{{ post.data.title }}">
    {% else %}
        <div id="{{ id }}__placeholder"></div>
    {% endif %}

    <main>
        {{ content | safe }}
    </main>
</div>
