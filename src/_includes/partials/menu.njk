{%- set id = "" | componentId() -%}
<style>
#{{id}} {
    position: fixed;
    top: -1px;
    left: 0;
    right: 0;
    padding: 1em 0;
    z-index: {{ site.baseZIndex }};
}
#{{id}}::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(var(--background), .8);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
    backdrop-filter: saturate(180%) blur(20px);
    z-index: {{ site.baseZIndex + 1 }};
}
.{{ id }}__scrolled {
    border-bottom: 1px solid rgba(233, 236, 239, .5);
}
#{{ id }} nav {
    position: sticky;
    z-index: {{ site.baseZIndex + 2 }};
    width: var(--content-width);
    margin: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
@media (max-width: 1300px) {
    #{{ id }} nav {
        padding: 0 1em;
    }
}
#{{ id }}__wrapper {
    display: flex;
    align-items: center;
    gap: 1em;
}
#{{ id }}__logo {
    display: flex;
    align-items: center;
    gap: .35em;
    text-decoration: none;
    color: inherit;
    padding: .4em .5em .5em;
    font-weight: 700;
    font-size: 1.5em;
    user-select: none;
}
#{{ id }}__logo * {
    transition: fill 0.3s ease, transform 0.3s ease;
}
#{{ id }}__logo:hover svg {
    transform: scale(1.3);
}
#{{ id }}__logo:hover svg path {
    fill: none;
}
#{{ id }}__logo:hover svg > rect {
    stroke: #000;
    stroke-width: 60;
    rx: 160;
}
#{{ id }}__logo:hover svg g rect {
    fill: #000;
}
#{{ id }}__logo:hover svg circle {
    fill: rgb(233, 21, 45);
    stroke: rgb(var(--background));
}
#{{id}} menu {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
}
#{{id}} menu a {
    position: relative;
    display: block;
    text-decoration: none;
    color: inherit;
    padding: 1em;
    font-size: .9em;
    font-weight: 700;
}
#{{id}} menu a::after {
    content: "";
    position: absolute;
    bottom: .5em;
    left: .5em;
    right: .5em;
    height: 1px;
    background-color: currentColor;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}
#{{id}} menu a:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}
#{{id}} menu a:not(:hover)::after {
    transform-origin: right;
}
#{{ id }}__cta {
    margin-left: 1em;
    background: rgb(var(--box-background));
    border-radius: 1.5em;
    box-shadow: var(--btn-shadow);
    user-select: none;
}
#{{id}}__cta::after {
    display: none;
}
#{{ id }}__cta:hover {
    background: rgb(248, 249, 250);
}
#{{ id }}__cta:focus {
    box-shadow: none;
}
#{{ id }}__mobile {
    display: none;
}
@media (max-width: 790px) {
    #{{id}} {
        padding: 0;
    }
    #{{id}} nav {
        padding: 0;
    }
    #{{ id }}__logo {
        margin-right: 2.3em;
        padding: .7em .9em
    }
    #{{id}} menu {
        display: none;
        padding: 0 1em;
    }
    #{{id}} menu a {
        font-size: 1.5em;
        padding: .3em .7em;
    }
    #{{id}}.{{id}}__open {
        height: 101vh;
    }
    #{{id}}.{{id}}__open nav,
    #{{id}}.{{id}}__open #{{ id }}__wrapper,
    #{{id}}.{{id}}__open menu
    {
        display: block;
        display: block
    }
    #{{id}}.{{id}}__open #{{ id }}__cta {
        margin-left: 0;
        margin-top: .5em;
        padding: .6em 1.5em;
        text-align: center;
        display: inline-block;
    }
    #{{ id }}__mobile {
        display: block;
        position: absolute;
        top: .4em;
        right: .4em;
        padding: 1em;
    }
    #{{ id }}__mobile svg {
        display: block;
    }
}
</style>
<div id="{{id}}">
    <nav>
        <div id="{{ id }}__wrapper">
            <a href="/" id="{{ id }}__logo">
{#                <svg width="32" height="32" viewBox="0 0 770 770" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="630" cy="140" r="140" fill="black"/><path d="M418.282 80C412.888 99.0733 410 119.199 410 140H224C183.807 140 157.27 140.047 136.935 141.708C117.3 143.312 108.786 146.118 103.681 148.72C88.6278 156.39 76.3896 168.628 68.7197 183.681C66.1182 188.786 63.3122 197.3 61.708 216.935C60.0466 237.27 60 263.807 60 304V546C60 586.193 60.0466 612.73 61.708 633.065C63.3122 652.7 66.1182 661.214 68.7197 666.319C76.3896 681.372 88.6278 693.61 103.681 701.28C108.786 703.882 117.3 706.688 136.935 708.292C157.27 709.953 183.807 710 224 710H466C506.193 710 532.73 709.953 553.065 708.292C572.7 706.688 581.214 703.882 586.319 701.28C601.372 693.61 613.61 681.372 621.28 666.319C623.882 661.214 626.688 652.7 628.292 633.065C629.953 612.73 630 586.193 630 546V360C650.801 360 670.927 357.112 690 351.718V546C690 624.407 690 663.611 674.741 693.559L674.105 694.79C660.648 720.569 639.49 741.529 613.559 754.741L612.148 755.445C584.267 769.076 548.121 769.941 480.249 769.996L466 770H224L209.751 769.996C141.879 769.941 105.733 769.076 77.8516 755.445L76.4414 754.741C50.0987 741.319 28.6811 719.901 15.2588 693.559C0.953467 665.483 0.0597846 629.271 0.00390625 560.249L0 546V304C0 226.818 -0.000379905 187.623 14.5547 157.852L15.2588 156.441C28.4713 130.51 49.4313 109.352 75.21 95.8945L76.4414 95.2588C106.389 79.9998 145.593 80 224 80H418.282Z" fill="black"/><mask id="path-3-inside-1_127_18" fill="white"><rect x="140" y="220" width="200" height="165" rx="40"/></mask><rect x="140" y="220" width="200" height="165" rx="40" fill="black" stroke="black" stroke-width="120" mask="url(#path-3-inside-1_127_18)"/><path d="M204 495H486C497.696 495 504.664 495.023 509.829 495.445C512.973 495.702 514.299 496.042 514.658 496.153C516.445 497.094 517.904 498.554 518.846 500.341C518.956 500.699 519.298 502.024 519.555 505.171C519.977 510.336 520 517.304 520 529V566C520 577.696 519.977 584.664 519.555 589.829C519.298 592.974 518.957 594.299 518.846 594.658C517.905 596.445 516.445 597.905 514.658 598.846C514.299 598.957 512.974 599.298 509.829 599.555C504.664 599.977 497.696 600 486 600H204C192.304 600 185.336 599.977 180.171 599.555C177.024 599.298 175.699 598.956 175.341 598.846C173.554 597.905 172.094 596.445 171.153 594.658C171.042 594.299 170.702 592.973 170.445 589.829C170.023 584.664 170 577.696 170 566V529C170 517.304 170.023 510.336 170.445 505.171C170.702 502.025 171.042 500.699 171.153 500.341C172.094 498.554 173.554 497.094 175.341 496.153C175.699 496.042 177.025 495.702 180.171 495.445C185.336 495.023 192.304 495 204 495Z" fill="black" stroke="black" stroke-width="60"/></svg>#}
                <svg width="32" height="32" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="m730.436 0c102.078.00002967 139.094 10.6288 176.413 30.5869 37.318 19.9582 66.606 49.2459 86.564 86.5641 19.957 37.319 30.587 74.335 30.587 176.413v436.872c0 102.078-10.63 139.094-30.587 176.413-19.958 37.318-49.246 66.606-86.564 86.564-37.319 19.957-74.335 30.587-176.413 30.587h-436.872c-102.078 0-139.094-10.63-176.413-30.587-37.3182-19.958-66.6059-49.246-86.5641-86.564-19.6463-36.736-30.252435-73.178-30.5790875-171.676l-.0078125-4.737v-436.872c.00002755-102.078 10.6288-139.094 30.5869-176.413 19.9582-37.3182 49.2459-66.6059 86.5641-86.5641 37.319-19.9581 74.335-30.58687246 176.413-30.5869z" fill="#000"/><rect height="650" rx="120" stroke="#fff" stroke-width="40" width="650" x="174" y="200"/><circle cx="784" cy="240" fill="#ff3b30" r="180" stroke="#000" stroke-width="80"/><g fill="#fff"><rect height="155" rx="40" width="200" x="294" y="320"/><rect height="155" rx="40" width="410" x="294" y="575"/></g></svg>

                Appio
            </a>
            <menu>
                <li>
                    <a href="/#how-it-works">How it works</a>
                </li>
                <li>
                    <a href="/#case-studies">Case studies</a>
                </li>
                <li>
                    <a href="/#pricing">Pricing</a>
                </li>
            </menu>
        </div>
        <menu>
            <li>
                <a href="https://demo.appio.so/">Demo</a>
            </li>
            <li>
                <a id="{{ id }}__cta" href="/list/">Get started</a>
            </li>
        </menu>
        <div id="{{ id }}__mobile">
            <svg width="24" height="24" viewBox="0 0 18 18">
                <polyline fill="none" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" points="2 12, 16 12">
                    <animate id="{{ id }}__bottom-open" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 2 12, 16 12; 2 9, 16 9; 3.5 15, 15 3.5"></animate>
                    <animate id="{{ id }}__bottom-close" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 3.5 15, 15 3.5; 2 9, 16 9; 2 12, 16 12"></animate>
                </polyline>
                <polyline fill="none" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" points="2 5, 16 5">
                    <animate id="{{ id }}__top-open" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 2 5, 16 5; 2 9, 16 9; 3.5 3.5, 15 15"></animate>
                    <animate id="{{ id }}__top-close" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 3.5 3.5, 15 15; 2 9, 16 9; 2 5, 16 5"></animate>
                </polyline>
            </svg>
        </div>
    </nav>
</div>
<script>
    (function () {
        // Scrolling border
        const
            header = document.getElementById("{{ id }}"),
            scrollClass = "{{ id }}__scrolled";
        window.addEventListener("scroll", () => {
            if (window.scrollY > 0) {
                header.classList.add(scrollClass);
            } else {
                header.classList.remove(scrollClass);
            }
        });

        // Mobile menu
        let open = false;
        const
            openClass = "{{ id }}__open",
            mobileMenu = document.getElementById("{{ id }}__mobile"),
            bottomOpen = document.getElementById("{{ id }}__bottom-open"),
            bottomClose = document.getElementById("{{ id }}__bottom-close"),
            topOpen = document.getElementById("{{ id }}__top-open"),
            topClose = document.getElementById("{{ id }}__top-close");

        function toggle(state) {
            open = state;
            if (open) {
                header.classList.add(openClass);
                bottomOpen.beginElement();
                topOpen.beginElement();
                document.body.style.overflow = "hidden";
            } else {
                header.classList.remove(openClass);
                bottomClose.beginElement();
                topClose.beginElement();
                document.body.style.overflow = "visible";
            }
        }

        mobileMenu.addEventListener("click", () => {
            toggle(!open)
        });

        header.querySelectorAll("a").forEach(function (a) {
            if (a.getAttribute("href").startsWith("/#")) {
                a.addEventListener("click", function () {
                    toggle(false);
                })
            }
        })
    })();
</script>