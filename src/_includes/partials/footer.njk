{%- set id = "" | componentId() -%}
<style>
#{{ id }}__container {
    position: fixed;
    top: 0; bottom: 0;
    left: 0; right: 0;
    z-index: -1;
    color: #b0aea5;
    font-size: .8em;
    background: rgb(var(--outer-background-color));
    padding: 5em 0;
    letter-spacing: 0;
}
#{{ id }} footer {
    position: fixed;
    bottom: 2em;
    left: 0; right: 0;
}
#{{ id }} footer > div {
    max-width: var(--content-width);
    margin: 0 auto;
    padding: 0 var(--content-padding);
}
#{{ id }} menu {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
#{{ id }} menu:last-of-type {
    padding-bottom: 0.5em;
    border-bottom: solid 1px rgba(176, 174, 165, .2);
}
#{{ id }} a,
#{{ id }} span
{
    position: relative;
    display: block;
    padding: .5em 1em;
    text-decoration: none;
    color: #b0aea5;
    white-space: nowrap;
}
#{{ id }} a:hover {
    text-decoration: underline;
}
#{{ id }} span {
    opacity: .6;
}
#{{ id }} span::after {
    content: "→";
    font-size: .8em;
    margin-left: .3em;
}
#{{ id }} aside {
    padding-left: 1em;
    font-size: .85em;
    max-width: var(--content-width);
    margin: 1em auto;
}
#{{ id }} a[target]::after {
    position: absolute;
    content: "";
    display: inline-block;
    top: 0.5em;
    right: 0;
    border: 0.25em solid transparent;
    border-bottom-color: currentColor;
    transform: rotate(45deg);
    opacity: .5;
}
#{{ id }} a[target]:hover::after {
    opacity: 1;
}
</style>
<div id="{{ id }}">
    <div id="{{ id }}__container">
        <footer>
            <div>
                <menu>
                    <li>
                        <a href="/blog/">Blog</a>
                    </li>
                    <li>
                        <a href="/case-studies/">Case studies</a>
                    </li>
            {#        <li>#}
            {#            <a href="/integrations">Integrations</a>#}
            {#        </li>#}
                    <li>
                        <a href="/contact/">Contact</a>
                    </li>
                    <li>
                        <a href="/legal">Legal</a>
                    </li>
                    <li>
                        <a href="https://docs.appio.so/" target="_blank">Docs</a>
                    </li>
                    <li>
                        <a href="https://status.appio.so/" target="_blank" rel="noopener noreferrer">Status</a>
                    </li>
                </menu>
                <menu>
                    <li>
                        <span>Integrations</span>
                    </li>
                    <li>
                        <a href="/integrations/zapier/">Zapier</a>
                    </li>
                </menu>
                <aside>
                    © {{ "now" | date("YYYY") }} Appio Limited. All rights reserved.
                </aside>
            </div>
        </footer>
    </div>
</div>