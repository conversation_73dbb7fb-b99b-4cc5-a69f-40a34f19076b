{%- set id = "" | componentId() -%}
<style>
#{{ id }} {
    padding-top: 4em;
}
#{{ id }} h1 {
    font-size: 3.5em;

    /*opacity: 0;*/
    /*transform: translateY(30px);*/
    /*animation: fadeIn .5s ease-out .3s forwards;*/
}
#{{ id }} h2 {
    font-size: 2em;
    line-height: 1.4;
    font-weight: 400;

    opacity: 0;
    animation: fadeIn .3s ease-out .3s forwards;
}
#{{ id }} h2 span.{{ id }}__no-wrap {
    white-space: nowrap;
}
#{{ id }} h2 span.{{ id }}__tag {
    display: inline-block;
    color: rgb(86, 1, 89);
    background: rgb(249, 226, 251);
    /*text-transform: capitalize;*/
    font-weight: 600;
}
#{{ id }} h2 span.{{ id }}__tag_left {
    padding-left: .25em;
    border-top-left-radius: .2em;
    border-bottom-left-radius: .2em;
}
#{{ id }} h2 span.{{ id }}__tag_right {
    padding-right: .25em;
    border-top-right-radius: .2em;
    border-bottom-right-radius: .2em;
}
#{{ id }} h2 span.{{ id }}__tag_blue {
    color: rgb(0, 61, 90);
    background: rgb(201, 240, 255);
}
#{{ id }} h2 span.{{ id }}__scratch {
    position: relative;
    white-space: nowrap;
}
#{{ id }} h2 span.{{ id }}__scratch:after {
    content: "";
    position: absolute;
    bottom: .45em;
    left: -.1em;
    right: -.2em;
    height: .09em;
    background-color: currentColor;
    border-radius: 1em;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform .1s ease-in 1s; /* ease-out */
}
#{{ id }} h2 span.{{ id }}__scratch.js-in-viewport:after {
    transform: scaleX(1);
}
#{{ id }} div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 3em;

    opacity: 0;
    animation: fadeIn .2s ease-out .4s forwards;
}
#{{ id }} nav {
    display: flex;
    gap: 1em;
}
#{{ id }} a {
    display: block;
    text-decoration: none;
    padding: .8em 1.5em;
    border-radius: 1.5em;
    box-shadow: var(--box-shadow);
    font-weight: 700;
    font-size: 1.4em;
    color: inherit;
    background: rgb(var(--box-background));
    user-select: none;
    text-align: center;
}
#{{ id }} a:hover {
    background: rgb(248, 249, 250);
}
#{{ id }} a.{{ id }}__main {
    background: rgb(var(--btn-color));
    color: #fff;
    box-shadow: rgba(0, 0, 0, 0.1) 0 1px 1px 0, rgba(0, 0, 0, 0.08) 0 2px 3px 0, rgba(0, 0, 0, 0.12) 0 4px 8px 0, rgba(6, 54, 109, 0.4) 0 -3px 2px 0 inset, rgba(255, 255, 255, 0.14) 0 2px .4px 0 inset;
}
#{{ id }} a.{{ id }}__main:hover {
    background: rgb(var(--btn-hover-color));
}
#{{ id }} a:focus {
    box-shadow: none;
}
#{{ id }} aside {
    display: flex;
    align-items: center;
    gap: .5em;
    font-size: 1.1em;
    font-weight: 500;
    color: rgb(110,110,110);
    white-space: nowrap;
    user-select: none;
    text-transform: lowercase;
}
#{{ id }} aside svg {
    height: 1.5em;
    display: block;
}
@media (max-width: 1300px) {
    #{{ id }} {
        padding-top: 2em;
    }
}
@media (max-width: 790px) {
    #{{ id }} {
        padding-top: 2em;
    }
    #{{ id }} h1 {
        font-size: 3em;
    }
    #{{ id }} h2 {
        font-size: 1.4em;
        margin-top: .6em;
    }
    #{{ id }} aside {
        display: none;
    }
}
@media (max-width: 490px) {
    #{{ id }} h1 {
        font-size: 2.3em;
    }
    #{{ id }} h2 {
        font-size: 1.1em;
        margin-top: 1.5em;
    }
    #{{ id }} div {
        margin-top: 2em;
    }
    #{{ id }} nav {
        flex-direction: column;
        width: 100%;
    }
    #{{ id }} a {
        font-size: 1.2em;
    }
}
</style>
<div id="{{ id }}">
{#    <h1>#}
{#        You have the web app.<br>#}
{#        Now add mobile features.#}
{#    </h1>#}
{#    <h1>Expand your web app to mobile</h1>#}
    <h1>
        Your customers are on mobile.<br>
        What is stopping you?
{#        Meet them there.#}
    </h1>
    <h2>
        Appio adds mobile features to your web app, without <span class="{{ id }}__scratch">building an app</span>.<br>
        Send <span class="{{ id }}__tag {{ id }}__tag_left">push&nbsp;</span><span class="{{ id }}__no-wrap"><span class="{{ id }}__tag {{ id }}__tag_right">notifications</span>,</span>
        create <span class="{{ id }}__tag {{ id }}__tag_left">home</span><span class="{{ id }}__tag">&nbsp;screen&nbsp;</span><span class="{{ id }}__no-wrap"><span class="{{ id }}__tag {{ id }}__tag_right">widgets</span>,</span>
        launch <span class="{{ id }}__tag {{ id }}__tag_left {{ id }}__tag_blue">in&nbsp;</span><span class="{{ id }}__no-wrap"><span class="{{ id }}__tag {{ id }}__tag_right {{ id }}__tag_blue">minutes</span>.</span>

    </h2>
    <div>
        <nav>
            <a class="{{ id }}__main" href="/list/">Start Now</a>
            <a href="https://demo.appio.so/">Try the Demo</a>
        </nav>
        <aside aria-hidden="true">
            Works on
            {% include "../../assets/images/platform_ios.svg" %}
            {% include "../../assets/images/platform_android.svg" %}
            <span style="opacity:.5">{% include "../../assets/symbols/phone.svg" %}</span>
            <span style="opacity:.5">{% include "../../assets/symbols/tablet.svg" %}</span>
            {#<span style="opacity:.5">{% include "../../assets/symbols/watch.svg" %}</span>#}
        </aside>
    </div>
</div>
<script>
(function () {
    document.querySelectorAll(".{{ id }}__scratch").forEach(el => {
        window.viewPortObserver.observe(el);
    });
})();
</script>