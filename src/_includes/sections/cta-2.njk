{%- set id = "" | componentId() -%}
<style>
#{{ id }} {
    margin: var(--section-gap) auto 0;
    text-align: center;
}
#{{ id }} h2 {
    font-size: 3em;
}
#{{ id }} #cta-start {
    margin: 2em auto;
}
#{{ id }} p span {
    color: rgb(128, 0, 0);
    background: rgb(255, 210, 225);
    padding: .1em .2em;
    border-radius: .2em;
    font-weight: 700;
}

#{{ id }}__cta {
    text-decoration: none;
    font-weight: 700;
}
#{{ id }}__cta::after {
    content: "→";
    display: inline-block;
    margin-left: .25em;
    transition: transform 0.3s ease, opacity 0.3s ease;
    animation: none;
    font-weight: 500;
}
#{{ id }}__cta:hover::after {
    animation: arrowFly 0.6s ease forwards;
}
@media (max-width: 490px) {
    #{{ id }} h2 {
        font-size: 1.4em;
    }
}
</style>
<div id="{{ id }}">
    <h2>
        Give your users the mobile<br>
        experience they’ve been waiting for.
    </h2>

    <style>{% include "../../assets/button/styles.css" %}</style>
    <div id="cta-start" class="frame" data-href="/list/">
        <img alt="image" src="/assets/button/base.svg" class="frame__base">
        <img alt="image" src="/assets/button/key.svg" class="frame__key">
        <img alt="image" src="/assets/button/cover.svg" class="frame__cover">
        <span class="frame__text">Start</span>
    </div>
{#    <script src="/assets/button/scripts.js"></script>#}
    <script>(function () { {% include "../../assets/button/scripts.js" %} })();</script>
    <noscript>
        <a href="https://appio.so/list/">Start</a>
    </noscript>

    <p>
        <span>No</span> app stores. <span>No</span> mobile developers. <span>No</span> slowdowns.
        <br><br>
        Not convinced yet?<br>
        <a id="{{ id }}__cta" href="https://demo.appio.so/">See Appio in action</a>
    </p>
</div>