{%- set data = [
{
question: "Is <PERSON><PERSON><PERSON> an app builder?",
answer: "No. Appio doesn’t build mobile apps. It gives your web app real mobile features like push notifications and widgets, without needing native development or dealing with app stores."
},
{
question: "How does <PERSON><PERSON><PERSON> save me money?",
answer: "No developers, no mobile design, no agencies, and no ongoing maintenance costs. Fast results at a fraction of the cost."
},
{
question: "How can <PERSON>pp<PERSON> make me money?",
answer: "Offer mobile features as part of your higher-tier plans, or use them to reduce churn and boost engagement. Get ahead of your competitors by offering better features."
},
{
question: "How does <PERSON><PERSON><PERSON> integrate into my current web app?",
answer: "Insert the short JavaScript snippet into your web app and automate actions via our <a href=\"https://docs.appio.so/\" target=\"_blank\">API</a>, or no-code tools like Zapier."
},
{
question: "Can I customise Appio to match my brand?",
answer: "Yes. You control your brand across the entire user journey. Upload your logo, title, and description."
},
{
question: "Is Appio secure and compliant?",
answer: "Yes. Appio does not store any personal data. Privacy is built in by design."
},
{
question: "Why are push notifications better than email?",
answer: "Reliable delivery. No spam filters, no tracking pixels, no false-positive reporting. Just direct delivery with reliable open tracking."
},
{
question: "Does Appio provide an SDK?",
answer: "No. Appio does not integrate into existing mobile apps, it completely removes the need for them. This is one of Appio’s biggest advantages. Appio is a subscription-based service (SaaS) that you control via our web app or <a href=\"https://docs.appio.so/\" target=\"_blank\">API</a>. There’s no SDK to install or manage."
},
{
question: "Who created Appio?",
answer: "Appio is being built by a team led by serial entrepreneur <a href=\"https://www.michalgondar.com/\" target=\"_blank\">Michal Gondar</a>."
}
] -%}
{%- set id = "" | componentId() -%}
<style>
#{{ id }} {
    margin: var(--section-gap) auto 0;
}
#{{ id }} h3 {
    text-align: center;
    font-size: 3em;
}
#{{ id }} main {
    margin: 2.5em auto 0;
    display: flex;
    flex-direction: column;
    border-radius: 1.5em;
    background: rgb(var(--box-background));
    border-color: #fafafa;
    box-shadow: var(--box-shadow);
    padding: .5em 1.5em;
    max-width: 60em;
}
#{{ id }} main > div {
    cursor: pointer;
    border-bottom: solid 1px rgb(var(--border-color));
    padding: 1em 0;
    font-size: 1.1em;
}
#{{ id }} main > div:last-child {
    border-bottom: none;
}
#{{ id }} main > div > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
#{{ id }} main h4 {
    font-weight: 600;
    font-size: 1.1em;
}
#{{ id }} main p {
    font-size: .9em;
    margin: 0;
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    transition: max-height 0.2s ease, opacity 0.2s ease, padding 0.2s ease;
}
#{{ id }} main .open p {
    max-height: 200em;
    opacity: 1;
    margin-top: .4em;
}
#{{ id }} main svg {
    transition: transform 0.2s ease;
}
#{{ id }} main .open svg {
    transform: rotate(180deg);
}
@media (max-width: 1150px) {
    #{{ id }} h3 {
        font-size: 2.5em;
    }
}
@media (max-width: 590px) {
    #{{ id }} main {
        margin-top: 1.5em;
    }
    #{{ id }} h3 {
        font-size: 2em;
        text-align: left;
        padding: 0 .5em;
    }
}
</style>
<div id="{{ id }}">
    <h3>Frequently Asked Questions</h3>

    <main>
        {%- for item in data %}
            <div>
                <div>
                    <h4>{{ item.question }}</h4>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7797 6.22007C11.9201 6.3607 11.999 6.55132 11.999 6.75007C11.999 6.94882 11.9201 7.13945 11.7797 7.28007L8.52968 10.5301C8.38906 10.6705 8.19843 10.7494 7.99968 10.7494C7.80093 10.7494 7.61031 10.6705 7.46968 10.5301L4.21968 7.28007C4.0872 7.1379 4.01508 6.94985 4.01851 6.75555C4.02194 6.56125 4.10065 6.37586 4.23806 6.23845C4.37547 6.10104 4.56086 6.02233 4.75516 6.0189C4.94946 6.01547 5.13751 6.08759 5.27968 6.22007L7.99968 8.94007L10.7197 6.22007C10.8603 6.07962 11.0509 6.00073 11.2497 6.00073C11.4484 6.00073 11.6391 6.07962 11.7797 6.22007Z" fill="#000"></path></svg>
                </div>
                <p>{{ item.answer | safe }}</p>
            </div>
        {%- endfor %}
    </main>
</div>
<script>
    (function () {
        const items = document.querySelectorAll("#{{ id }} main > div");
        items.forEach(i => {
            i.addEventListener("click", () => {
                if (i.classList.contains("open")) {
                    i.classList.remove("open");
                } else {
                    i.classList.add("open");
                }
            });
        });
    })();
</script>