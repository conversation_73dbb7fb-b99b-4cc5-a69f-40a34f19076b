{%- set id = "" | componentId() -%}
<style>
#{{ id }} {
    margin: var(--section-gap) auto 0;
    display: flex;
    gap: 2em;
    max-width: 40em;
    height: 334px;
    overflow: hidden;
    border-radius: 1.5em;
    box-shadow: var(--box-shadow);
    background: rgb(var(--box-background));
    padding: 1em;
}
#{{ id }} img {
    border-radius: .8em;
    height: 300px;
    object-fit: cover;
    object-position: top;
}
#{{ id }} h4 {
    margin-top: .5em;
    font-size: 2.5em;
}
#{{ id }}__cta {
    display: flex;
    justify-content: space-between;
    align-items: start;
}
#{{ id }} a {
    position: relative;
    text-decoration: none;
    color: inherit;
    display: block;
    margin-top: .5em;
    font-weight: 700;
    font-size: 1.2em;
}
#{{id}} a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background-color: currentColor;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}
#{{id}} a:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}
#{{id}} a:not(:hover)::after {
    transform-origin: right;
}
@media (max-width: 700px) {
    #{{ id }} {
        height: auto;
        gap: 1.5em;
    }
    #{{ id }} h4 {
        margin-top: 0;
    }
}
@media (max-width: 550px) {
    #{{ id }} {
        flex-direction: column;
    }
    #{{ id }} h4 {
        font-size: 2em;
    }
    #{{ id }} img {
        width: 100%;
        height: 100vw;
    }
}}
</style>
<div id="{{ id }}">
    <img src="/assets/images/tom.jpg" alt="Tom" width="200" height="200">
    <div>
        <h4>Meet Tom</h4>
        <p>
            Tom runs a SaaS business.<br>
            His users wanted mobile notifications and widgets.<br>
            But building a mobile app would take too long.<br>
            Tom found Appio and delivered both in minutes.<br>
            His users love it.<br>
        </p>
        <div id="{{ id }}__cta">
            <a href="/list/">Be like Tom!</a>
        </div>
    </div>
</div>