{%- set id = "" | componentId() -%}
<style>
#case-studies {
    /* extra scroll so on ios the pricing menu is not visible */
    scroll-margin-top: calc(var(--scroll-margin-top) + 1rem);
    max-width: var(--content-width);
    margin: auto;
    padding: 0 var(--content-padding);
}
#{{ id }} {
    margin: var(--section-gap) auto 0;
}
#{{ id }} h3 {
    font-size: 4em;
    text-align: center;
}
#{{ id }} div > p {
    text-align: center;
}
#{{ id }} div > p span {
    font-weight: 600;
    padding: 0 .15em;
    /*text-transform: capitalize;*/
}
#{{ id }} div > p span.{{ id }}__real {
    color: rgb(0, 61, 90);
    background: rgb(201, 240, 255);
}
#{{ id }} div > p span.{{ id }}__illustrative {
    color: rgb(86, 1, 89);
    background: rgb(249, 226, 251);
}
#{{ id }} main {
    margin-top: 2em;
    overflow-x: auto;

    -ms-overflow-style: none;
    scrollbar-width: none;
    scroll-snap-type: x mandatory;
}
#{{ id }} main div {
    display: flex;
    gap: 4rem;
    padding: .5rem 36rem;
}
#{{ id }} main a {
    position: relative;
    display: block;
    min-width: 28rem;
    box-shadow: var(--box-shadow);
    background: rgb(var(--box-background));
    border-radius: 1.5em;
    padding: 1.5em 2em 3em;
    overflow: hidden;
    text-decoration: none;
    scroll-snap-align: center;
}
#{{ id }} main a.{{ id }}__illustrative::after {
    content: "illustrative";
    position: absolute;
    top: 1em;
    right: -2em;
    transform: rotate(45deg);
    color: rgb(86, 1, 89);
    background: rgb(249, 226, 251);
    padding: .2em 2em;
    font-weight: 500;
}
#{{ id }} main a h4 {
    font-size: 2em;
}
#{{ id }} main a:hover h4 {
    color: rgb(var(--btn-color));
}
#{{ id }} main a span {
    font-weight: 700;
    position: absolute;
    bottom: 1em;
    left: 2em;
}
#{{ id }} main a:hover span {
    color: rgb(var(--btn-color));
}
#{{ id }} main a span::after {
    content: "→";
    display: inline-block;
    margin-left: .25em;
    transition: transform 0.3s ease, opacity 0.3s ease;
    animation: none;
    font-weight: 500;
}
#{{ id }} main a:hover span::after {
    animation: arrowFly 0.6s ease forwards;
}
#{{ id }} main img {
    width: 50%;
    padding-bottom: .5em;
}
#{{ id }} aside {
    margin-top: 1em;
    display: flex;
    gap: 1em;
    justify-content: center;
}
#{{ id }} aside svg {
    display: block;
    cursor: pointer;
    user-select: none;
    background-color: rgb(232, 232, 237);
    border-radius: 50%;
    fill: rgba(0, 0, 0, 0.56);
}
#{{ id }} aside svg:hover,
#{{ id }} aside svg:focus
{
    background-color: rgb(236, 236, 239);
    fill: rgba(0, 0, 0, 0.64);
}
#{{ id }} aside svg:active {
    background-color: rgb(223, 223, 228);
}
@media (max-width: 790px) {
    #{{ id }} h3 {
        font-size: 3em;
    }
}
@media (max-width: 590px) {
    #case-studies {
        margin: 0 1em;
    }
    #{{ id }} h3 {
        font-size: 2em;
        text-align: left;
    }
    #{{ id }} div > p {
        text-align: left;
    }
}
@media (max-width: 550px) {
    #{{ id }} main a {
        min-width: 80vw;
    }
}
</style>
<div id="{{ id }}">
    <div id="case-studies">
        <h3>Case studies</h3>
        <p>
            Some of these examples are <span class="{{ id }}__illustrative">illustrative</span>,
            inspired by <span class="{{ id }}__real">real product needs</span>.
            They show how Appio can add value today.
        </p>
    </div>

    <main id="{{ id }}__slider">
        <div>
        {%- for caseStudy in collections.caseStudies %}
            <a href="{{ caseStudy.url }}" {% if not caseStudy.data.real %}class="{{ id }}__illustrative"{% endif %}>
                {% if caseStudy.data.logo %}
                    <img src="/assets/logos/{{ caseStudy.data.logo }}" alt="{{ caseStudy.data.title }}">
                {% else %}
                    <h4>{{ caseStudy.data.company }}</h4>
                {% endif %}
                <p>{{ caseStudy.data.description }}</p>
                <span>Read more</span>
            </a>
        {%- endfor %}
        </div>
    </main>
    <aside>
        <svg id="{{ id }}__left" width="36" height="36" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg"><path d="M20 25c-.384 0-.768-.146-1.06-.44l-5.5-5.5a1.5 1.5 0 0 1 0-2.12l5.5-5.5a1.5 1.5 0 1 1 2.12 2.12L16.622 18l4.44 4.44A1.5 1.5 0 0 1 20 25z"></path></svg>
        <svg id="{{ id }}__right" width="36" height="36" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg" ><path d="M22.56 16.938l-5.508-5.5a1.493 1.493 0 0 0-2.116.003 1.502 1.502 0 0 0 .004 2.121L19.384 18l-4.444 4.438A1.502 1.502 0 0 0 15.996 25c.382 0 .764-.145 1.056-.438l5.508-5.5a1.502 1.502 0 0 0 0-2.125z"></path></svg>
    </aside>
</div>
<script>
    (function () {
        let
            index = 0,
            stepWidth = 0;

        const
            left = document.getElementById("{{ id }}__left"),
            right = document.getElementById("{{ id }}__right"),
            slider = document.getElementById("{{ id }}__slider"),
            sliderStrip = slider.querySelector("div"),
            items = slider.querySelectorAll("a")

        function setup() {
            const
                sliderWidth = slider.getBoundingClientRect().width,
                gap = parseFloat(window.getComputedStyle(sliderStrip).gap),
                itemWidth = items[0] && items[0].getBoundingClientRect().width,
                padding = (sliderWidth - itemWidth) / 2;

            stepWidth = itemWidth + gap;
            items.forEach(item => item.style.width = `${itemWidth}px`);
            sliderStrip.style.paddingLeft = `${padding}px`
            sliderStrip.style.width = (itemWidth * items.length + gap * (items.length - 1) + 2 * padding) + "px";
            slide(1);
        }

        function slide(delta) {
            index = (index + delta + items.length) % items.length;
            slider.scrollTo({
                left: index * stepWidth,
                behavior: "smooth"
            })
        }

        // Start
        setup();

        window.addEventListener("resize", () => {
            setup();
            slide(1);
        });
        slider.addEventListener("scroll", () => {
            index  = Math.round(slider.scrollLeft / stepWidth);
        })
        left.addEventListener("click", (e) => {
            e.preventDefault();
            slide(-1)
        });
        right.addEventListener("click", (e) => {
            e.preventDefault();
            slide(1)
        });
    })()
</script>