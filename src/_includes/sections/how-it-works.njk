{%- set id = "" | componentId() -%}
{%- set zIndex = site.baseZIndex  -%}
<style>
#how-it-works {
    scroll-margin-top: var(--scroll-margin-top);
}
#how-it-works p {
    margin-top: 0;
}
#{{ id }} {
    --li-margin: 10px;
    margin-top: var(--section-gap);
}
#{{ id }} > div {
    margin: 0 1.3em;
}
#{{ id }} h3 {
    font-size: 3em;
    line-height: 1.2em;
}
#{{ id }} main {
    margin-top: 1.5em;
    display: flex;
    gap: 1em;
}
#{{ id }} aside {
    max-width: 21em; /* firefox and safari */
    font-size: .9em;
}
#{{ id }} aside > p {
    margin-left: 1.5em;
    font-size: .93em;
}
#{{ id }} ul,
#{{ id }} li
{
    list-style: none;
    margin: 0;
    padding: 0;
}
#{{ id }} ul {
    position: relative;
}
#{{ id }} li {
    transition-property: all;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-duration: .2s;
    border-radius: 1em;
    padding: 1em 1em 1em 1.5em;
    cursor: pointer;
    margin-bottom: var(--li-margin);
}
#{{ id }} li:hover {
    opacity: .8;
    background-color: rgb(var(--box-background));
}
#{{ id }} li h4 {
    font-size: 1.1em;
    font-weight: 700;
}
#{{ id }} li h4 span {
    color: rgb(110,110,110);
    margin-right: .3em;
    user-select: none;
}
#{{ id }} li p {
    margin-top: .3em;
}
#{{ id }} li.{{ id }}__active {
    background: rgb(var(--box-background));
    box-shadow: var(--box-shadow);
}
#{{ id }} li.{{ id }}__active h4 {
    color: rgb(var(--btn-color));
}
#{{ id }}__select-wrapper {
    position: relative;
    display: inline-block;
}
#{{ id }}__select-wrapper::after {
    content: "";
    position: absolute;
    right: .7em;
    top: 55%;
    transform: translateY(-50%);
    font-size: .5em;
    pointer-events: none;
    opacity: .5;
    width: 1em;
    height: 1em;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' stroke='currentColor'%3E%3Cpath d='M7 9L12 4' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M17 9L12 4' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M7 15L12 20' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M17 15L12 20' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
}
#{{ id }} select {
    cursor: pointer;
    border: solid 1px transparent;
    background-color: rgb(218, 249, 212);
    color: rgb(1, 89, 1);
    font-size: .7em;
    font-weight: 700;
    border-radius: .5em;
    padding: .3em 1.3em .3em .6em;
    appearance: none;
    overflow: hidden;
    text-overflow: ellipsis;
    box-shadow: var(--box-shadow);
    vertical-align: middle;
}
/* class `js` set by javascript */
#{{ id }}__select-wrapper.js {
    position: relative;
}
#{{ id }}__select-wrapper.js select {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
}
#{{ id }}__select-wrapper.js span {
    display: block;
    background-color: rgb(218, 249, 212);
    color: rgb(1, 89, 1);
    border-radius: .5em;
    padding: .2em 1.2em .2em .4em;
    cursor: pointer;
    text-align: center;
}
#{{ id }}__select-wrapper.js:hover span {
    background-color: rgba(218, 249, 212, .5);
}
#{{ id }}__video {
    flex: 1;
    border-radius: 1.5em;
    background: rgb(var(--box-background));
    border-color: #fafafa;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    justify-content: center;
    aspect-ratio: 16 / 9;
    overflow: hidden;
}
#{{ id }}__video p {
    font-size: 3em;
    opacity: .3;
}

@supports (text-orientation: upright) {
    #{{ id }}__for-you::before,
    #{{ id }}__for-your-users::before
    {
        writing-mode: vertical-rl;
        text-orientation: upright;
        opacity: .1;
        font-weight: 700;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        transition: top 0.3s ease;
        white-space: nowrap;
        font-size: 1.3em;
        left: -2em;
    }
    #{{ id }}__for-you::before {
        content: "YOU";
        /* `--top-extra` is modified via JavaScript */
        top: calc(.75em + var(--top-extra, 0px));
        transform: none;
    }
    #{{ id }}__for-your-users::before {
        content: "YOUR USERS";
    }
}

@media (max-width: 1150px) {
    #{{ id }} h3 {
        font-size: 2.5em;
    }
}
/* Breakpoint value used in JavaScript bellow as well */
@media (max-width: 990px) {
    #{{ id }} main {
        scroll-margin-top: var(--scroll-margin-top);
        flex-direction: column;
        margin-top: 1em;
        gap: calc(1em - var(--li-margin));
    }
    #{{ id }}__for-you::before,
    #{{ id }}__for-your-users::before
    {
        content: "";
        display: none;
    }
    #{{ id }}__select-wrapper {
        display: block;
        margin: .2em -.5em 0;
    }
    #{{ id }}__select-wrapper span {
        border-right: .35em;
    }
    #how-it-works p {
        margin-top: 1em;
    }
    #{{ id }} aside {
        max-width: none;
    }
    #{{ id }} li {
        position: relative;
        padding-right: 2.5em;
    }
    #{{ id }} ul.{{ id }}__open {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: {{ zIndex }};
        padding: 1em;
    }
    #{{id}} ul.{{ id }}__open::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(var(--background), .8);
        -webkit-backdrop-filter: saturate(180%) blur(20px);
        backdrop-filter: saturate(180%) blur(20px);
        z-index: {{ zIndex + 1 }};
    }
    #{{ id }} ul.{{ id }}__open li {
        position: relative;
        z-index: {{ zIndex + 2 }};
    }
    #{{ id }} ul:not(.{{ id }}__open) li:not(.{{ id }}__active) {
        display: none;
    }
    #{{ id }} ul:not(.{{ id }}__open) li::after {
        content: "";
        position: absolute;
        right: .8em;
        top: 55%;
        transform: translateY(-50%);
        pointer-events: none;
        opacity: .5;
        width: 1.15em;
        height: 1.15em;
        background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' stroke='currentColor'%3E%3Cpath d='M7 9L12 4' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M17 9L12 4' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M7 15L12 20' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M17 15L12 20' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
    }
}
@media (max-width: 490px) {
    #{{ id }} > div {
        margin: 0 1em;
    }
    #{{ id }} h3 {
        font-size: 2em;
    }
}
</style>

<div id="{{ id }}">
    <div id="how-it-works">
        <h3>
            See how Appio works for
            <label id="{{ id }}__select-wrapper" for="{{ id }}__select">
                <select id="{{ id }}__select">
                    <option value="{{ id }}__for-you">You</option>
                    <option value="{{ id }}__for-your-users">Your Users</option>
                </select>
                <span></span>
            </label>
        </h3>
        <p>From setup to mobile engagement, here’s what happens.</p>
    </div>
    <main>
        <aside>
            <ul id="{{ id }}__for-you">
                <li class="{{ id }}__active" data-time="0:00">
                    <h4>
                        <span aria-hidden="true">0:00</span>
                        Set up your brand
                    </h4>
                    <p>
                        Upload your logo, title, and description.
                        Your identity is front and center.
                    </p>
                </li>
                <li data-time="0:40">
                    <h4>
                        <span aria-hidden="true">0:40</span>
                        Install JavaScript snippet
                    </h4>
                    <p>Paste the short snippet into your app and link the <var>user</var> field. That’s it.</p>
                </li>
                <li data-time="0:55">
                    <h4>
                        <span aria-hidden="true">0:55</span>
                        Create a widget
                    </h4>
                    <p>Design your widget and connect it to your live data.</p>
                </li>
                <li data-time="1:20">
                    <h4>
                        <span aria-hidden="true">1:20</span>
                        Send a notification
                    </h4>
                    <p>Use our web app, API, or one of our no-code tool.</p>
                </li>
            </ul>

            <ul id="{{ id }}__for-your-users" style="display:none;">
                <li class="{{ id }}__active" data-time="2:00">
                    <h4>
                        <span aria-hidden="true">2:00</span>
                        Onboard
                    </h4>
                    <p>User taps a link or scans a QR and lands in your branded flow, no signup needed.</p>
                </li>
                <li data-time="2:10">
                    <h4>
                        <span aria-hidden="true">2:10</span>
                        Add the widget
                    </h4>
                    <p>They're guided to add your widget to their home screen.</p>
                </li>
                <li data-time="2:20">
                    <h4>
                        <span aria-hidden="true">2:20</span>
                        Get notified
                    </h4>
                    <p>Instant, interactive and persistent. No spam folders or missed updates.</p>
                </li>
            </ul>
        </aside>
        <div id="{{ id }}__video">
            {# TODO each step will have associated short video demonstrating the action #}
            <p aria-hidden="true">Videos coming soon...</p>
        </div>
    </main>
</div>
<noscript>
    <style>
        #{{ id }} {
            animation: fadeIn .5s ease-out .3s forwards
        }
    </style>
</noscript>
<script>
    (function () {
        // Small screen detection
        let isSmall, isOpen = false;
        const mediaQuery = window.matchMedia("(max-width: 990px)")

        function handleViewportChange(e) {
            isSmall = e.matches
        }

        handleViewportChange(mediaQuery);
        mediaQuery.addEventListener("change", handleViewportChange);

        // `For who` and items selection
        const
            rootEl = document.getElementById("{{ id }}"),
            selectWrapper = document.getElementById('{{ id }}__select-wrapper'),
            select = document.querySelector("#{{ id }}__select"),
            selectValue = selectWrapper.querySelector("span"),
            uls = document.querySelectorAll("#{{ id }}__for-you, #{{ id }}__for-your-users"),
            lis = document.querySelectorAll("#{{ id }}__for-you li, #{{ id }}__for-your-users li"),
            activeClass = "{{ id }}__active",
            openClass = "{{ id }}__open";

        selectValue.innerText = select.options[select.selectedIndex].text;
        selectWrapper.classList.add("js");

        function resetActive() {
            lis.forEach(l => {
                l.classList.remove(activeClass);
            });
        }

        function switchTo(sectionId, index, scroll) {
            resetActive();

            const el = document.querySelector(`#${sectionId} li:nth-child(${index + 1})`);
            el.classList.add(activeClass);

            if (sectionId === "{{ id }}__for-you") {
                const
                    margin = getComputedStyle(rootEl).getPropertyValue("--li-margin"),
                    height = el.getBoundingClientRect().height + parseFloat(margin);
                document.querySelector(`#${sectionId}`).style.setProperty("--top-extra", `${height * index}px`);
            }

            // Scroll video to view
            if (scroll && isSmall) {
                rootEl.querySelector("main").scrollIntoView({ behavior: "instant" });
            }

            // Seek and play video
            seekAndPlayVideo(el.dataset.time);
        }

        function pauseVideo() {
            // TODO: pause video
            console.log("pause video");
        }

        function seekAndPlayVideo(time) {
            // TODO: seek and play video
            console.log("seek and play video", time);
        }

        function open(sectionId) {
            isOpen = true;
            document.getElementById(sectionId).classList.add(openClass);
            document.body.style.overflow = "hidden";

            pauseVideo();
        }

        function close(sectionId) {
            isOpen = false;
            document.getElementById(sectionId).classList.remove(openClass);
            document.body.style.overflow = "visible";
        }

        select.addEventListener("change", (e) => {
            uls.forEach(u => {
                u.style.display = "none";
            });
            const el = document.querySelector(`#${e.target.value}`);
            el.style.display = "block";
            switchTo(el.id, 0, false);

            selectValue.innerText = select.options[select.selectedIndex].text;
        });

        select.addEventListener("focus", () => {
            pauseVideo();
        });

        uls.forEach(u => {
            const lis = u.querySelectorAll("li");
            lis.forEach((l, i) => {
                l.index = i;
                l.addEventListener("click", () => {
                    // Fix post-css animation
                    fixCssAnimation()

                    if (isSmall && !isOpen) {
                        open(u.id);
                    } else {
                        switchTo(u.id, l.index, true);
                        close(u.id);
                    }
                });
            });
        });

        function fixCssAnimation() {
            const animatedParent = rootEl.closest(".js-in-viewport");
            if (! animatedParent) return;
            animatedParent.classList.remove("js-in-viewport");
            animatedParent.style.opacity = 1;
        }
    })();
</script>