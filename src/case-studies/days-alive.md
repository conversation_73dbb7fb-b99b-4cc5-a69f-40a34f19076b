---
# widget displaying remaining days alive like DeathClock: https://www.unoptimal.com/push-off

layout: layouts/case-study_layout.njk
title: "Days Alive Case Study"
company: "Days Alive"
description: "Minimalist widget showing days since signup, birthday, or milestone."
permalink: "/case-studies/days-alive/"
tags: ["case-study"]
order: 7
---

# Days Alive

Minimalist widget showing days since signup, birthday, or milestone.

## The Challenge

Users wanted a simple way to track meaningful life milestones and stay motivated by seeing their progress. They needed a clean, always-visible counter for important dates.

## The Solution

Using Appio, Days Alive implemented:
- **Minimalist home screen widget** with customizable counters
- **Milestone notifications** for significant day counts
- **Multiple counter support** for different life events

## The Results

- Increased user engagement with personal milestones
- Higher retention through daily widget interaction
- Positive user feedback on motivation and mindfulness
