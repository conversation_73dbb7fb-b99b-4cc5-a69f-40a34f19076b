---
excludeFromSitemap: true
---
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>My Appio</title>
    <link rel="icon" id="favicon" type="image/x-icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">

    <script src="https://js-de.sentry-cdn.com/05b59c0f63ed916d9deebc51defe534b.min.js" crossorigin="anonymous"></script>
    <script>
        window.Sentry && Sentry.onLoad(function() {
            Sentry.init({
                replaysSessionSampleRate: 0.0, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
                replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
            });
        });
    </script>

    <link rel="stylesheet" href="https://cdn.appio.so/appio.css">
    <style>
        body {
            padding: 3em 3em 5em;
            max-width: 800px;
            margin: auto;
        }
        h1 {
            margin-bottom: .5em;
        }
        img {
            background: #000;
            padding: 9px;
            border-radius: 100%;
            margin-bottom: 2em;
        }
    </style>
</head>
<body>
<img src="https://cdn.appio.so/app/appio.so/logo.png" alt="Appio logo" width="100" height="100">
<h1>Thank you for joining the Appio waiting list!</h1>

<p>
    We’ve received your details.<br>
    You’re now on the list for early access and exclusive updates.
</p>

<h2>Want a sneak peek?</h2>
<p>
    Explore our <a href="https://demo.appio.so/?source=waitinglist">Demo</a> to see Appio in action.
</p>

<h2>Let's talk!</h2>
<p>
    Talk to a human, not an AI!<br>
    I'd love to hear what you're working on and explore how Appio might help.<br>
    You can <a href="https://cal.com/appio/30min">book a quick call</a> with me or reach out directly at <a href="mailto:<EMAIL>?subject=Waiting%20List%20Question"><EMAIL></a><br>
    <br>
    <em>— Michal, founder of Appio.so</em>
</p>
</body>
</html>
