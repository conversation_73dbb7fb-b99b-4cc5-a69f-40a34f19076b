---
layout: layouts/main_layout.njk
canonical: "https://appio.so/"
---
{%- set id = "" | componentId() -%}
<style>
    .{{ id }}__fade-in {
        opacity: 0;
    }
    .{{ id }}__fade-in.js-in-viewport {
        animation: fadeIn .3s ease-out var(--fade-in-delay, .2s) forwards;
    }
</style>
<noscript>
    <style>
        .{{ id }}__fade-in {
            opacity: 1;
        }
    </style>
</noscript>

<section class="{{ id }}__fade-in" style="--fade-in-delay: .1s;">
    {% include "sections/hero.njk" %}
</section>
<section class="{{ id }}__fade-in">
    {% include "sections/how-it-works.njk" %}
</section>
<section class="{{ id }}__fade-in">
    {% include "sections/tom.njk" %}
</section>
<section class="{{ id }}__fade-in">
    {% include "sections/who-is-it-for.njk" %}
</section>
<section class="{{ id }}__fade-in">
    {% include "sections/what-will-you-achieve.njk" %}
</section>
<section class="{{ id }}__fade-in">
    {% include "sections/cta-1.njk" %}
</section>
<section class="{{ id }}__fade-in">
    {% include "sections/benefits.njk" %}
</section>
<section class="{{ id }}__fade-in">
    {% include "sections/features.njk" %}
</section>
<div class="{{ id }}__fade-in">
    {% include "sections/case-studies.njk" %}
</div>
<section class="{{ id }}__fade-in">
    {% include "sections/pricing.njk" %}
</section>
<section class="{{ id }}__fade-in">
    {% include "sections/faq.njk" %}
</section>
<section class="{{ id }}__fade-in">
    {% include "sections/cta-2.njk" %}
</section>

<script>
    (function () {
        document.querySelectorAll(".{{ id }}__fade-in").forEach(s => {
            window.viewPortObserver.observe(s);
        });
    })();
</script>